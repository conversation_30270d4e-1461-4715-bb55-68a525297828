from fastapi import FastAP<PERSON>, Request, File, UploadFile, Form, WebSocket, WebSocketDisconnect
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
import uvicorn
import os
import json
import base64
import tempfile
import aiofiles
# import openai  # Comentado, não é mais necessário
from dotenv import load_dotenv
from pathlib import Path
import difflib
import uuid
import asyncio
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from contextlib import asynccontextmanager # Importar asynccontextmanager
import re # <<< ADICIONAR IMPORTAÇÃO DE RE

# Carrega variáveis de ambiente
load_dotenv()

# Configura OpenAI API Key - comente esta linha se usar Whisper local
# openai.api_key = os.getenv("OPENAI_API_KEY")  # Comentado, não é mais necessário

SONGS: Dict[str, Any] = {}
WHISPER_MODEL: Optional[Any] = None

# Definir SONGS_DIR e TEMP_DIR aqui para serem usados em load_songs_on_startup
TEMP_DIR = Path("temp")
SONGS_DIR = Path("songs")
STATIC_DIR = Path("static")
AUDIO_DIR = STATIC_DIR / "audio"

def load_songs_on_startup():
    songs_data = {}
    # Garante que os diretórios existam
    SONGS_DIR.mkdir(parents=True, exist_ok=True)
    AUDIO_DIR.mkdir(parents=True, exist_ok=True) 
    
    example_songs_data = {
        "musica1": {"titulo": "Evidências", "artista": "Chitãozinho & Xororó", "letra": "Quando eu digo que deixei de te amar\nÉ porque eu te amo\nQuando eu digo que não quero mais você\nÉ porque eu te quero\nVocê me deixa sem saber o que fazer\nÉ brincadeira com o meu coração"},
        "musica2": {"titulo": "Cheia de Manias", "artista": "Raça Negra", "letra": "Cheia de manias\nToda dengosa\nCheinha de graça\nUm monte de coisas\nCoisa mais linda\nCoisa mais gostosa\nTão delicada, carinhosa"}
    }

    if not any(SONGS_DIR.iterdir()): # Verifica se o diretório está vazio
        print("Nenhuma música encontrada, criando exemplos...")
        for song_id, song_d in example_songs_data.items():
            with open(SONGS_DIR / f"{song_id}.json", "w", encoding="utf-8") as f:
                json.dump(song_d, f, ensure_ascii=False, indent=4)
    
    for file_path in SONGS_DIR.glob("*.json"):
        song_id = file_path.stem
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                song_info = json.load(f)
                
                # <<< Verifica e adiciona URLs de LRC e Áudio >>>
                lrc_file = SONGS_DIR / f"{song_id}.lrc"
                if lrc_file.exists():
                    # Passaremos a URL para o cliente buscar
                    song_info["lrc_url"] = f"/songs/{song_id}.lrc" 
                else:
                    song_info["lrc_url"] = None
                    print(f"[Warning] Arquivo LRC não encontrado para {song_id}: {lrc_file}")

                # Tenta encontrar arquivo de áudio (ex: mp3, ogg)
                audio_file = None
                for ext in ["mp3", "ogg", "wav", "m4a"]: # Adicione mais extensões se necessário
                    potential_audio_file = AUDIO_DIR / f"{song_id}.{ext}"
                    if potential_audio_file.exists():
                        audio_file = potential_audio_file
                        break 
                
                if audio_file:
                    # Passa a URL relativa que o servidor estático entende
                    song_info["audio_url"] = f"/static/audio/{audio_file.name}" 
                else:
                    song_info["audio_url"] = None
                    print(f"[Warning] Arquivo de Áudio não encontrado para {song_id} em {AUDIO_DIR}")
                
                songs_data[song_id] = song_info
        except json.JSONDecodeError as e:
            print(f"Erro ao carregar JSON da música {file_path}: {e}")
        except Exception as e:
            print(f"Erro inesperado ao carregar música {file_path}: {e}")
            
    return songs_data

# Mover a definição de lifespan para ANTES de instanciar o app
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Importa whisper aqui dentro!
    import whisper 
    global SONGS, WHISPER_MODEL
    print("Lifespan: Iniciando.")
    print(f"Lifespan: Estado inicial WHISPER_MODEL: {WHISPER_MODEL}")

    print("Lifespan: Carregando músicas...")
    SONGS = load_songs_on_startup()
    print(f"Lifespan: {len(SONGS)} músicas carregadas.")

    if WHISPER_MODEL is None:
        print("Lifespan: WHISPER_MODEL is None, carregando modelo Whisper (medium)...")
        try:
            # Agora 'whisper' está definido localmente neste escopo
            WHISPER_MODEL = whisper.load_model("medium") 
            print("Lifespan: Modelo Whisper (medium) carregado com sucesso!")
        except Exception as e:
            print(f"Erro CRÍTICO ao carregar modelo Whisper: {e}")
            WHISPER_MODEL = None
    else:
        print("Lifespan: WHISPER_MODEL JÁ ESTAVA CARREGADO. Não carregando novamente.")

    TEMP_DIR.mkdir(exist_ok=True)
    yield
    print("Lifespan: Servidor encerrando.")

# Agora instanciar o app
app = FastAPI(title="Karaokê em Duelo com Lobbies", lifespan=lifespan)

# Monta diretórios de templates e arquivos estáticos
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")
app.mount("/songs", StaticFiles(directory=SONGS_DIR), name="songs")

# Diretório para armazenar arquivos de áudio temporários
TEMP_DIR = Path("temp")
TEMP_DIR.mkdir(exist_ok=True)

# Diretório para as letras das músicas
SONGS_DIR = Path("songs")

# Variáveis globais para armazenar dados carregados
SONGS: Dict[str, Any] = {}
WHISPER_MODEL: Optional[Any] = None # Usar Optional para tipagem correta

# Função para carregar músicas (movida para fora para ser chamada no lifespan)
def load_songs_on_startup():
    songs_data = {}
    # Garante que os diretórios existam
    SONGS_DIR.mkdir(parents=True, exist_ok=True)
    AUDIO_DIR.mkdir(parents=True, exist_ok=True) 
    
    example_songs_data = {
        "musica1": {"titulo": "Evidências", "artista": "Chitãozinho & Xororó", "letra": "Quando eu digo que deixei de te amar\nÉ porque eu te amo\nQuando eu digo que não quero mais você\nÉ porque eu te quero\nVocê me deixa sem saber o que fazer\nÉ brincadeira com o meu coração"},
        "musica2": {"titulo": "Cheia de Manias", "artista": "Raça Negra", "letra": "Cheia de manias\nToda dengosa\nCheinha de graça\nUm monte de coisas\nCoisa mais linda\nCoisa mais gostosa\nTão delicada, carinhosa"}
    }

    if not any(SONGS_DIR.iterdir()): # Verifica se o diretório está vazio
        print("Nenhuma música encontrada, criando exemplos...")
        for song_id, song_d in example_songs_data.items():
            with open(SONGS_DIR / f"{song_id}.json", "w", encoding="utf-8") as f:
                json.dump(song_d, f, ensure_ascii=False, indent=4)
    
    for file_path in SONGS_DIR.glob("*.json"):
        song_id = file_path.stem
        with open(file_path, "r", encoding="utf-8") as f:
            song_info = json.load(f)
            
            # <<< Verifica e adiciona URLs de LRC e Áudio >>>
            lrc_file = SONGS_DIR / f"{song_id}.lrc"
            if lrc_file.exists():
                # Passaremos a URL para o cliente buscar
                song_info["lrc_url"] = f"/songs/{song_id}.lrc" 
            else:
                song_info["lrc_url"] = None
                print(f"[Warning] Arquivo LRC não encontrado para {song_id}: {lrc_file}")

            # Tenta encontrar arquivo de áudio (ex: mp3, ogg)
            audio_file = None
            for ext in ["mp3", "ogg", "wav", "m4a"]: # Adicione mais extensões se necessário
                potential_audio_file = AUDIO_DIR / f"{song_id}.{ext}"
                if potential_audio_file.exists():
                    audio_file = potential_audio_file
                    break 
            
            if audio_file:
                # Passa a URL relativa que o servidor estático entende
                song_info["audio_url"] = f"/static/audio/{audio_file.name}" 
            else:
                song_info["audio_url"] = None
                print(f"[Warning] Arquivo de Áudio não encontrado para {song_id} em {AUDIO_DIR}")
            
            songs_data[song_id] = song_info
    return songs_data

# Função para transcrever áudio usando Whisper local
async def transcribe_audio(audio_path):
    try:
        if WHISPER_MODEL is None:
            raise Exception("Modelo Whisper não carregado.")
        result = WHISPER_MODEL.transcribe(audio_path)
        return result.get("text", "")
        
        # API da OpenAI (comentado, não é mais utilizado)
        """
        with open(audio_path, "rb") as audio_file:
            transcript = openai.Audio.transcribe(
                "whisper-1", 
                audio_file, 
                language="pt"
            )
        return transcript.get("text", "")
        """
    except Exception as e:
        print(f"Erro na transcrição: {e}")
        return ""

# Função para calcular a pontuação
def calculate_score(original_lyrics, transcription):
    # Normaliza as strings
    original = original_lyrics.lower().replace("\n", " ").split()
    transcribed = transcription.lower().split()
    
    # Usa difflib para comparar sequências
    matcher = difflib.SequenceMatcher(None, original, transcribed)
    
    # Calcula a porcentagem de correspondência
    match_blocks = matcher.get_matching_blocks()
    match_size = sum(block.size for block in match_blocks if block.size > 0)
    max_size = max(len(original), len(transcribed))
    
    if max_size == 0:
        return 0
    
    # Retorna pontuação de 0 a 100
    return round((match_size / max_size) * 100)

# <<< NOVA FUNÇÃO PARA EXTRAIR TEXTO DO LRC >>>
def extract_text_from_lrc(song_id: str) -> Optional[str]:
    lrc_file_path = SONGS_DIR / f"{song_id}.lrc"
    if not lrc_file_path.exists():
        print(f"[LRC Extraction] Arquivo LRC não encontrado: {lrc_file_path}")
        return None
    
    all_lyrics_lines = []
    # Regex para capturar o texto após o timestamp. Ex: [00:00.81]Texto da letra
    # Captura tudo depois do ']' e remove espaços extras no início/fim da linha de texto
    lrc_line_regex = re.compile(r"^\s*\[[^\]]+\]\s*(.*?)\s*$", re.IGNORECASE)
    
    try:
        with open(lrc_file_path, "r", encoding="utf-8") as f:
            for line_content in f:
                match = lrc_line_regex.match(line_content)
                if match:
                    text = match.group(1).strip() # .strip() para remover espaços em branco no início/fim
                    if text: # Adiciona apenas se houver texto
                        all_lyrics_lines.append(text)
        if not all_lyrics_lines:
            print(f"[LRC Extraction] Nenhuma linha de letra encontrada no arquivo LRC: {lrc_file_path}")
            return None
        return "\n".join(all_lyrics_lines)
    except Exception as e:
        print(f"Erro ao ler ou parsear arquivo LRC {lrc_file_path}: {e}")
        return None

class Player(BaseModel):
    client_id: str
    name: str
    websocket: Optional[WebSocket] = None
    score: int = 0
    transcription: str = ""
    audio_processed: bool = False
    is_processing_audio: bool = False
    ready_for_duet_start: bool = False
    processing_complete: bool = False
    processing_error: bool = False

    class Config:
        arbitrary_types_allowed = True

class Lobby(BaseModel):
    lobby_id: str
    players: Dict[str, Player] = {}
    song_id: Optional[str] = None
    song_lyrics: Optional[str] = None
    turn_client_id: Optional[str] = None # client_id do jogador da vez
    game_state: str = "waiting_for_players" # waiting_for_players, song_selection, player1_turn, player2_turn, results
    duet_mode: bool = False # <<< NOVO CAMPO PARA MODO DUETO
    host_client_id: Optional[str] = None # <<< ADICIONAR CAMPO PARA O HOST >>>

lobbies: Dict[str, Lobby] = {}

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, lobby_id: str, client_id: str):
        await websocket.accept()
        if lobby_id not in self.active_connections:
            self.active_connections[lobby_id] = []
        self.active_connections[lobby_id].append(websocket)
        
        lobby = lobbies.get(lobby_id)
        if lobby and client_id in lobby.players:
            lobby.players[client_id].websocket = websocket

    def disconnect(self, websocket: WebSocket, lobby_id: str, client_id: str):
        if lobby_id in self.active_connections:
            # Verificar se o websocket ainda está na lista antes de remover
            if websocket in self.active_connections[lobby_id]:
                self.active_connections[lobby_id].remove(websocket)
            if not self.active_connections[lobby_id]:
                del self.active_connections[lobby_id]
        
        lobby = lobbies.get(lobby_id)
        if lobby and client_id in lobby.players:
            lobby.players[client_id].websocket = None

    async def broadcast_to_lobby(self, lobby_id: str, message: Dict):
        if lobby_id in self.active_connections:
            connections_to_broadcast = list(self.active_connections[lobby_id])
            print(f"[Broadcast] Lobby {lobby_id}: Enviando para {len(connections_to_broadcast)} conexões.")
            for connection in connections_to_broadcast:
                try:
                    await connection.send_json(message)
                except RuntimeError as e:
                    print(f"[Broadcast Error] Lobby {lobby_id}: RuntimeError ao enviar para {connection.client.host}:{connection.client.port} - {e}") 
                except Exception as e:
                    print(f"[Broadcast Error] Lobby {lobby_id}: Exceção ao enviar para {connection.client.host}:{connection.client.port} - {type(e).__name__}: {e}")

manager = ConnectionManager()

async def notify_lobby_update(lobby_id: str):
    if lobby_id in lobbies:
        lobby = lobbies[lobby_id]
        players_data = {pid: p.model_dump(exclude={'websocket'}) for pid, p in lobby.players.items()}
        
        song_details_for_update = None
        if lobby.song_id and lobby.song_id in SONGS:
            song_details_for_update = SONGS[lobby.song_id].copy()
            # Garante que a letra correta (LRC processada ou do JSON) seja usada
            song_details_for_update["letra"] = lobby.song_lyrics 
            # Adiciona lrc_url e audio_url se não estiverem já na cópia (devem estar de load_songs_on_startup)
            if "lrc_url" not in song_details_for_update:
                song_details_for_update["lrc_url"] = SONGS[lobby.song_id].get("lrc_url")
            if "audio_url" not in song_details_for_update:
                song_details_for_update["audio_url"] = SONGS[lobby.song_id].get("audio_url")

        lobby_details = {
            "lobby_id": lobby.lobby_id,
            "players": players_data,
            "song_id": lobby.song_id,
            "song_details": song_details_for_update, # <<< INCLUIR song_details AQUI >>>
            "game_state": lobby.game_state,
            "turn_client_id": lobby.turn_client_id,
            "host_client_id": lobby.host_client_id, 
            "duet_mode": lobby.duet_mode
        }
        # Log para verificar o que está sendo enviado
        print(f"[Notify Lobby Update - DEBUG] Enviando para lobby {lobby_id}: {lobby_details}")
        await manager.broadcast_to_lobby(lobby_id, {"type": "lobby_update", "data": lobby_details})
    else:
        print(f"Tentativa de notificar lobby inexistente: {lobby_id}")

@app.get("/api/songs") # Novo endpoint para buscar a lista de músicas
async def get_available_songs():
    return SONGS # Retorna o dicionário global SONGS

@app.post("/lobbies/create")
async def create_lobby(player_name: str = Form("Jogador 1")):
    lobby_id = str(uuid.uuid4())[:6]
    client_id = str(uuid.uuid4())
    player = Player(client_id=client_id, name=player_name)
    lobbies[lobby_id] = Lobby(lobby_id=lobby_id, players={client_id: player}, host_client_id=client_id)
    print(f"Lobby {lobby_id} criado por {player_name} ({client_id}). Host: {client_id}")
    return {"lobby_id": lobby_id, "client_id": client_id, "player_name": player_name}

@app.post("/lobbies/{lobby_id}/join")
async def join_lobby(lobby_id: str, player_name: str = Form("Jogador 2")):
    lobby = lobbies.get(lobby_id)
    if not lobby:
        return JSONResponse(status_code=404, content={"message": "Lobby não encontrado"})
    if len(lobby.players) >= 2:
        # Verifica se um dos "players" tem websocket None, indicando que saiu e pode ser substituído
        can_replace = False
        for pid, p_data in lobby.players.items():
            if p_data.websocket is None:
                # Remove o jogador antigo para dar lugar ao novo
                del lobby.players[pid]
                can_replace = True
                break
        if not can_replace:
            return JSONResponse(status_code=400, content={"message": "Lobby cheio"})
    
    client_id = str(uuid.uuid4())
    while client_id in lobby.players:
        client_id = str(uuid.uuid4())

    player = Player(client_id=client_id, name=player_name)
    lobby.players[client_id] = player
    if len(lobby.players) == 2 and lobby.game_state == "waiting_for_players":
        lobby.game_state = "song_selection"
        # O turno inicial para seleção de música deve ser do host
        lobby.turn_client_id = lobby.host_client_id 

    print(f"{player_name} ({client_id}) entrou no lobby {lobby_id}")
    await notify_lobby_update(lobby_id)
    return {"lobby_id": lobby_id, "client_id": client_id, "player_name": player_name}

@app.websocket("/ws/{lobby_id}/{client_id}")
async def websocket_endpoint(websocket: WebSocket, lobby_id: str, client_id: str):
    lobby = lobbies.get(lobby_id)
    if not lobby or client_id not in lobby.players:
        print(f"Tentativa de conexão WebSocket falhou: lobby {lobby_id} ou cliente {client_id} não encontrado.")
        await websocket.accept() # Aceita para poder enviar erro e fechar
        await websocket.send_json({"type":"error", "message": "Lobby ou cliente inválido."})
        await websocket.close(code=1008)
        return

    await manager.connect(websocket, lobby_id, client_id)
    
    await asyncio.sleep(0.05) # Pequeno delay (50ms) para estabilização
    
    await notify_lobby_update(lobby_id) # Envia estado atual após o sleep
    
    try:
        while True:
            data = await websocket.receive_json()
            message_type = data.get("type")
            message_data = data.get("data", {})
            lobby = lobbies.get(lobby_id)

            if not lobby or client_id not in lobby.players:
                break 

            current_player = lobby.players[client_id]

            if message_type == "select_song":
                if lobby and client_id == lobby.host_client_id:
                    song_id = data.get("song_id")
                    duet_mode_selected = data.get("duet_mode", False) # <<< LER FLAG duet_mode >>>

                    if song_id and song_id in SONGS:
                        lobby.song_id = song_id
                        lobby.song_lyrics = SONGS[song_id].get("letra") # Carregar a letra padrão
                        
                        # Extrair letra do LRC se disponível e substituir a letra padrão
                        lrc_text = extract_text_from_lrc(song_id)
                        if lrc_text:
                            lobby.song_lyrics = lrc_text
                            print(f"Letra de {song_id} carregada do arquivo .lrc")
                        else:
                            print(f"Arquivo .lrc não encontrado para {song_id} ou está vazio, usando letra do JSON.")

                        lobby.duet_mode = duet_mode_selected # <<< ARMAZENAR NO LOBBY >>>

                        # Limpar status dos jogadores para nova rodada/música
                        for player_obj in lobby.players.values():
                            player_obj.score = 0
                            player_obj.transcription = ""
                            player_obj.audio_processed = False
                            player_obj.is_processing_audio = False
                            player_obj.ready_for_duet_start = False
                            player_obj.processing_complete = False
                            player_obj.processing_error = False
                        
                        if lobby.duet_mode:
                            lobby.game_state = "duet_performance"
                            lobby.turn_client_id = None # Nenhum turno específico no modo dueto simultâneo
                            print(f"Modo Dueto Real ativado para música {song_id} no lobby {lobby_id}.")
                        else:
                            lobby.game_state = "player1_turn" # Ou o estado apropriado para iniciar o jogo normal
                            # Definir quem começa (geralmente o host ou o primeiro jogador que entrou)
                            # Se houver uma ordem específica, aplicar aqui. Ex: O host sempre começa.
                            if lobby.players:
                                player_ids = list(lobby.players.keys())
                                # Tenta manter o host como primeiro a jogar se ele estiver presente
                                if lobby.host_client_id in player_ids:
                                    lobby.turn_client_id = lobby.host_client_id
                                else: # Caso contrário, o primeiro da lista (pode ser melhorado)
                                    lobby.turn_client_id = player_ids[0]
                            else:
                                lobby.turn_client_id = None # Não há jogadores para definir o turno
                            print(f"Modo padrão (revezamento) ativado para música {song_id}. Turno de: {lobby.turn_client_id}")

                        # A mensagem song_selected não é mais estritamente necessária se notify_lobby_update envia tudo,
                        # mas pode ser mantida para um evento mais específico de "música acabou de ser selecionada".
                        # Por enquanto, vamos remover o envio duplicado de detalhes da música aqui, 
                        # já que notify_lobby_update (chamado logo após) agora os inclui.
                        
                        # song_details_to_send = SONGS[lobby.song_id].copy()
                        # song_details_to_send["letra"] = lobby.song_lyrics 
                        # await manager.broadcast_to_lobby(lobby_id, {
                        # "type": "song_selected", 
                        # "song": song_details_to_send,
                        # "game_state": lobby.game_state,
                        # "turn_client_id": lobby.turn_client_id,
                        # "duet_mode": lobby.duet_mode 
                        # })
                        
                        # Apenas notifica que uma música foi selecionada, o estado completo virá com notify_lobby_update
                        await manager.broadcast_to_lobby(lobby_id, {
                            "type": "song_selected_event", # Renomeado para evitar confusão com os dados
                            "song_id": lobby.song_id,
                            "titulo": SONGS[lobby.song_id].get("titulo", "N/A") 
                        })
                        await notify_lobby_update(lobby_id) # Notifica todos sobre a mudança de estado e música (agora com song_details)
                    else:
                        await websocket.send_json({"type": "error", "message": "Música inválida selecionada."})
                else:
                    await websocket.send_json({"type": "error", "message": "Apenas o host pode selecionar músicas ou lobby não encontrado."})
            
            elif message_type == "audio_submission":
                print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Mensagem recebida. Game State: {lobby.game_state if lobby else 'N/A'}.") # Log aprimorado
                
                # <<< AJUSTE PARA MODO DUETO E REVEZAMENTO >>>
                allow_submission = False
                if lobby.duet_mode and lobby.game_state == "duet_performance":
                    if lobby.song_id and not current_player.audio_processed:
                        allow_submission = True
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Permitindo submissão em modo dueto.")
                    else:
                        print(f"[Audio Submission Duet Denied] Lobby {lobby_id}, Cliente {client_id}: Música não definida (ID: {lobby.song_id}) ou áudio já processado (Processed: {current_player.audio_processed}).")
                elif not lobby.duet_mode and lobby.turn_client_id == client_id:
                    if lobby.song_id and not current_player.audio_processed:
                        allow_submission = True
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Permitindo submissão em modo de revezamento (turno do jogador).")
                    else:
                        print(f"[Audio Submission Turn Denied] Lobby {lobby_id}, Cliente {client_id}: Música não definida ({lobby.song_id}) ou áudio já processado ({current_player.audio_processed}).")
                else:
                    # Log para o caso que está aparecendo no erro do usuário
                    print(f"[Audio Submission Denied Fallback] Lobby {lobby_id}, Cliente {client_id}: Condições não atendidas. Duet Mode: {lobby.duet_mode}, Game State: {lobby.game_state}, Turn: {lobby.turn_client_id}, Song ID: {lobby.song_id}, Processed: {current_player.audio_processed}")

                if allow_submission:
                    current_player.is_processing_audio = True
                    current_player.ready_for_duet_start = False # Resetar se estava pronto e agora está submetendo
                    await notify_lobby_update(lobby_id) # Notifica que P estA processando
                    
                    # <<< INÍCIO DAS MODIFICAÇÕES PARA PROGRESSO >>>
                    player_websocket = current_player.websocket
                    if player_websocket:
                        try:
                            await player_websocket.send_json({
                                "type": "processing_progress",
                                "progress": 10,
                                "status_message": "Preparando seu áudio..."
                            })
                            await asyncio.sleep(0.05) # Pequeno delay opcional
                        except Exception as e_ws_send:
                            print(f"[WebSocket Send Error] Lobby {lobby_id}, Cliente {client_id}: Não foi possível enviar progresso 10%. Erro: {e_ws_send}")
                    # <<< FIM DAS MODIFICAÇÕES PARA PROGRESSO >>>

                    audio_base64 = message_data.get("audio_data") # <<< REINTRODUZIR E USAR message_data >>>
                    if not audio_base64:
                        print(f"[Audio Submission Error] Lobby {lobby_id}, Cliente {client_id}: Dados de áudio ausentes na mensagem.")
                        await websocket.send_json({"type": "error", "message": "Dados de áudio inválidos ou ausentes"})
                        current_player.is_processing_audio = False # Resetar estado
                        await notify_lobby_update(lobby_id)
                        return # Alterado de continue para return pois estamos fora de um loop direto aqui

                    # <<< Adicionados Logs Detalhados e Try/Except Específico >>>
                    temp_file_path = None
                    try:
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Decodificando Base64 (tamanho: {len(audio_base64)} chars).")
                        # Remove o prefixo 'data:audio/...' se existir
                        if "," in audio_base64:
                            audio_base64_data = audio_base64.split(',')[1]
                        else:
                            audio_base64_data = audio_base64 # Assume que já está sem prefixo
                        audio_binary = base64.b64decode(audio_base64_data)
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Base64 decodificado (tamanho: {len(audio_binary)} bytes).")
                        
                        temp_file_path = TEMP_DIR / f"{lobby_id}_{client_id}_{lobby.song_id}.wav"
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Salvando áudio em {temp_file_path}.")
                        async with aiofiles.open(temp_file_path, mode='wb') as f:
                            await f.write(audio_binary)
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Arquivo salvo. Transcrevendo...")
                        
                        # <<< INÍCIO DAS MODIFICAÇÕES PARA PROGRESSO >>>
                        if player_websocket:
                            try:
                                await player_websocket.send_json({
                                    "type": "processing_progress",
                                    "progress": 25,
                                    "status_message": "Iniciando transcrição..."
                                })
                                await asyncio.sleep(0.05)
                            except Exception as e_ws_send:
                                print(f"[WebSocket Send Error] Lobby {lobby_id}, Cliente {client_id}: Não foi possível enviar progresso 25%. Erro: {e_ws_send}")
                        # <<< FIM DAS MODIFICAÇÕES PARA PROGRESSO >>>

                        # <<< ADICIONAR LOG PARA VERIFICAR TAMANHO DO ARQUIVO DE ÁUDIO >>>
                        try:
                            file_size = os.path.getsize(temp_file_path)
                            print(f"[Audio Submission] Tamanho do arquivo de áudio temporário ({temp_file_path}): {file_size} bytes.")
                            if file_size == 0:
                                print(f"[Audio Submission Warning] Arquivo de áudio temporário está VAZIO: {temp_file_path}")
                        except OSError as e_size:
                            print(f"[Audio Submission Warning] Não foi possível obter o tamanho do arquivo de áudio {temp_file_path}: {e_size}")
                        # <<< FIM DO LOG DE TAMANHO DE ARQUIVO >>>

                        transcription = await transcribe_audio(str(temp_file_path))
                        # <<< LOGAR TRANSCRIÇÃO COMPLETA >>>
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Transcrição: '{transcription}' (tamanho: {len(transcription)}).")
                        
                        # <<< INÍCIO DAS MODIFICAÇÕES PARA PROGRESSO >>>
                        if player_websocket:
                            try:
                                await player_websocket.send_json({
                                    "type": "processing_progress",
                                    "progress": 75,
                                    "status_message": "Calculando sua pontuação..."
                                })
                                await asyncio.sleep(0.05)
                            except Exception as e_ws_send:
                                print(f"[WebSocket Send Error] Lobby {lobby_id}, Cliente {client_id}: Não foi possível enviar progresso 75%. Erro: {e_ws_send}")
                        # <<< FIM DAS MODIFICAÇÕES PARA PROGRESSO >>>

                        if not lobby.song_lyrics:
                             print(f"[Audio Submission Error] Lobby {lobby_id}, Cliente {client_id}: Letra da música não encontrada no estado do lobby.")
                             raise ValueError("Letra da música não disponível para calcular pontuação.")
                        
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Calculando pontuação...")
                        score = calculate_score(lobby.song_lyrics, transcription)
                        print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Pontuação calculada: {score}%.")
                        
                        current_player.score = score
                        current_player.transcription = transcription
                        current_player.audio_processed = True
                        current_player.is_processing_audio = False
                        current_player.ready_for_duet_start = False
                        current_player.processing_complete = True
                        current_player.processing_error = False
                        
                        # <<< INÍCIO DAS MODIFICAÇÕES PARA PROGRESSO (100%) >>>
                        if player_websocket:
                            try:
                                await player_websocket.send_json({
                                    "type": "processing_progress",
                                    "progress": 100,
                                    "status_message": "Pronto!"
                                })
                                # Aumentar um pouco o delay para dar chance ao frontend de mostrar 100%
                                # antes que a próxima atualização de lobby_update esconda o indicador.
                                await asyncio.sleep(0.3) 
                            except Exception as e_ws_send:
                                print(f"[WebSocket Send Error] Lobby {lobby_id}, Cliente {client_id}: Não foi possível enviar progresso 100%. Erro: {e_ws_send}")
                        # <<< FIM DAS MODIFICAÇÕES PARA PROGRESSO (100%) >>>
                        
                        # Se todos os jogadores processaram (em modo dueto) ou se for modo de turno e o jogador atual processou
                        if lobby.duet_mode:
                            active_players_in_lobby = [p for p_id, p in lobby.players.items() if p.websocket is not None]
                            print(f"[Dueto Check] Jogadores ativos no lobby: {len(active_players_in_lobby)}") # <<< LOG JOGADORES ATIVOS
                            for p_debug in active_players_in_lobby:
                                print(f"[Dueto Check] Jogador {p_debug.client_id} ({p_debug.name}): audio_processed={p_debug.audio_processed}") # <<< LOG ESTADO DE CADA JOGADOR
                            
                            if not active_players_in_lobby:
                                print(f"[Audio Submission Duet] Lobby {lobby_id}: Nenhum jogador ativo restante. Indo para resultados (ou erro).")
                                lobby.game_state = "results" 
                            else:
                                all_active_players_processed_duet = all(p.audio_processed for p in active_players_in_lobby)
                                active_players_count = len(active_players_in_lobby)

                                if all_active_players_processed_duet:
                                    lobby.game_state = "results"
                                    print(f"Dueto: Todos os {active_players_count} jogadores ativos processaram. Mudando para resultados.")
                                else:
                                    processed_count = sum(1 for p in active_players_in_lobby if p.audio_processed)
                                    print(f"Dueto: Jogador {client_id} processou. {processed_count}/{active_players_count} jogadores ativos processaram. Aguardando outros.")
                        else: # Modo de revezamento
                            # (Código original para mudar turno ou ir para resultados no modo revezamento)
                            if len(lobby.players) < 2: # Só um jogador, vai direto pros resultados
                                lobby.game_state = "results"
                            else: # Troca o turno
                                # Obter lista de jogadores ativos (com websocket não nulo)
                                active_player_ids = [p_id for p_id, p in lobby.players.items() if p.websocket is not None]
                                
                                if len(active_player_ids) < 2:
                                    # Se só tiver um jogador ativo, vai para resultados
                                    lobby.game_state = "results"
                                else:
                                    # Encontrar o próximo jogador que não cantou ainda
                                    current_turn_index = active_player_ids.index(client_id) if client_id in active_player_ids else -1
                                    
                                    # Procurar próximo jogador que não processou o áudio ainda
                                    next_player_id = None
                                    for i in range(1, len(active_player_ids)):
                                        idx = (current_turn_index + i) % len(active_player_ids)
                                        player_id = active_player_ids[idx]
                                        if not lobby.players[player_id].audio_processed:
                                            next_player_id = player_id
                                            break
                                    
                                    if next_player_id:
                                        # Encontrou próximo jogador que não cantou
                                        lobby.turn_client_id = next_player_id
                                        lobby.game_state = f"player{(active_player_ids.index(next_player_id) % 2) + 1}_turn"
                                        print(f"[Turn Change] Lobby {lobby_id}: Próximo turno para {next_player_id}, estado: {lobby.game_state}")
                                    else:
                                        # Todos os jogadores já cantaram
                                        lobby.game_state = "results"
                                        print(f"[Turn Change] Lobby {lobby_id}: Todos os jogadores cantaram. Mudando para resultados.")
                            
                            print(f"[Turn Change/Results] Lobby {lobby_id}: Modo revezamento. Novo estado: {lobby.game_state}. Notificando...")
                            await notify_lobby_update(lobby_id)

                    except Exception as e:
                        print(f"[Audio Submission Error] Lobby {lobby_id}, Cliente {client_id}: Erro processando áudio: {type(e).__name__}: {e}")
                        # Adicionar envio de mensagem de erro de progresso, se desejado, ou apenas confiar no 'error' type.
                        # Por exemplo, para resetar a UI de progresso no cliente em caso de erro:
                        if player_websocket: # player_websocket pode não ter sido definido se o erro ocorreu antes
                            try:
                                await player_websocket.send_json({
                                    "type": "processing_progress", # Reutiliza o tipo para simplificar o handler do cliente
                                    "progress": 0, # Ou um valor especial como -1 para indicar erro
                                    "status_message": "Erro ao processar. Tente novamente.",
                                    "error": True # Flag adicional opcional
                                })
                                await asyncio.sleep(0.05)
                            except Exception as e_ws_send:
                                print(f"[WebSocket Send Error] Lobby {lobby_id}, Cliente {client_id}: Não foi possível enviar progresso de erro. Erro: {e_ws_send}")
                        
                        await websocket.send_json({"type": "error", "message": f"Erro interno ao processar áudio: {e}"})
                        current_player.audio_processed = False # Não foi processado com sucesso
                        current_player.processing_error = True  # Marcar que houve erro
                        current_player.score = 0 # Zerar score em caso de erro
                        current_player.transcription = "Erro na transcrição" # Indicar erro
                    
                    finally:
                        # Independentemente de sucesso ou falha, marca como completo e que não está mais processando
                        current_player.is_processing_audio = False
                        current_player.processing_complete = True 
                        print(f"[Audio Submission FINALLY] Lobby {lobby_id}, Cliente {client_id}: Processamento concluído (Sucesso: {not current_player.processing_error}). Notificando lobby...")
                        # Notificar imediatamente após terminar o processamento deste jogador
                        await notify_lobby_update(lobby_id)

                        # <<< REMOVIDO O CÓDIGO DE REMOÇÃO DE ARQUIVO TEMP >>> 
                        # if temp_file_path and os.path.exists(temp_file_path): ... 
                        if temp_file_path and os.path.exists(temp_file_path):
                            print(f"[Audio Submission Debug] Arquivo temporário NÃO foi removido para debug: {temp_file_path}")
                        else:
                            print(f"[Audio Submission Debug] Arquivo temporário não encontrado ou já removido: {temp_file_path}")
                else:
                     print(f"[Audio Submission] Lobby {lobby_id}, Cliente {client_id}: Recebido fora de hora (Turno: {lobby.turn_client_id}, Música: {lobby.song_id}, Processado: {current_player.audio_processed}).")
                     await websocket.send_json({"type": "error", "message": "Não é sua vez, áudio já processado ou música não selecionada."})
            
            elif message_type == "request_rematch_or_new_song":
                # O host (P1) pode resetar o jogo para uma nova seleção de música ou revanche
                # player_id = data.get("player_id") # O client_id do WebSocket já é o player_id
                if lobby:
                    if lobby.duet_mode:
                        # Em modo dueto, a revanche reseta o estado para seleção de música mantendo o modo dueto
                        lobby.game_state = "song_selection" 
                        # Não precisa resetar lobby.duet_mode = False aqui, a menos que queira forçar nova escolha
                    else:
                        # Lógica original para modo de revezamento
                        lobby.game_state = "song_selection"
                        lobby.turn_client_id = None # Limpa o turno para nova seleção
                    
                    lobby.song_id = None
                    lobby.song_lyrics = None
                    for player_obj in lobby.players.values():
                        player_obj.score = 0
                        player_obj.transcription = ""
                        player_obj.audio_processed = False
                        player_obj.is_processing_audio = False
                        player_obj.ready_for_duet_start = False
                        player_obj.processing_complete = False
                        player_obj.processing_error = False
                    
                    print(f"Lobby {lobby_id}: {'Host solicitou nova música' if message_type == 'request_new_song' else 'Revanche solicitada'}. Estado: {lobby.game_state}, Modo Dueto: {lobby.duet_mode}")
                    await notify_lobby_update(lobby_id)
                else:
                    await websocket.send_json({"type": "error", "message": "Lobby não encontrado para revanche/nova música."})

            # <<< PASSO 5: Backend - Lógica de Início Sincronizado para Dueto >>>
            elif message_type == "start_duet_request":
                if lobby and lobby.duet_mode and lobby.song_id and lobby.game_state == "duet_performance":
                    player = lobby.players.get(client_id)
                    if player:
                        player.ready_for_duet_start = True
                        print(f"Lobby {lobby_id}: Jogador {client_id} ({player.name}) está pronto para o dueto.")

                        # Verificar se todos os jogadores ativos no lobby estão prontos
                        active_players_in_lobby = [p for p_id, p in lobby.players.items() if p.websocket is not None] # Considera apenas conectados
                        
                        # Garante que estamos verificando apenas os jogadores que de fato estão participando ativamente.
                        # Se houver apenas um jogador ativo no lobby em modo dueto (outro saiu), podemos prosseguir ou tratar como erro.
                        # Por ora, se houver N jogadores ativos, todos os N devem estar prontos.
                        if len(active_players_in_lobby) < 1: # Deve haver pelo menos 1 para dueto (idealmente 2)
                             await websocket.send_json({"type": "error", "message": "Não há jogadores suficientes para iniciar o dueto."})
                             player.ready_for_duet_start = False # Resetar se não pode começar
                             return

                        all_ready = all(p.ready_for_duet_start for p in active_players_in_lobby)

                        if all_ready:
                            print(f"Lobby {lobby_id}: Todos os {len(active_players_in_lobby)} jogadores estão prontos. Enviando begin_duet_now para: {[p.client_id for p in active_players_in_lobby]}.")
                            await manager.broadcast_to_lobby(lobby_id, {"type": "begin_duet_now"})
                            # Resetar o estado de prontidão para a próxima vez
                            for p_obj in lobby.players.values():
                                p_obj.ready_for_duet_start = False
                        else:
                            waiting_for_count = sum(1 for p in active_players_in_lobby if not p.ready_for_duet_start)
                            print(f"Lobby {lobby_id}: Jogador {client_id} pronto. Aguardando {waiting_for_count} outro(s) jogador(es).")
                            # Pode enviar uma notificação para o cliente que está aguardando, se desejar.
                            # await websocket.send_json({"type": "info", "message": "Aguardando o outro jogador clicar em Começar Dueto..."})
                    else:
                        await websocket.send_json({"type": "error", "message": "Jogador não encontrado no lobby."})
                else:
                    await websocket.send_json({"type": "error", "message": "Não é possível iniciar o dueto neste estado ou modo."})

    except WebSocketDisconnect as e:
        # Log mais detalhado da desconexão esperada
        print(f"[WebSocketDisconnect] Cliente {client_id} (Lobby {lobby_id}) desconectou com código: {e.code}, razão: {e.reason}")
    except Exception as e:
        # Log para exceções inesperadas dentro do loop do websocket
        print(f"[WebSocket Error] Exceção inesperada no handler para Cliente {client_id} (Lobby {lobby_id}): {type(e).__name__}: {e}")
        # Considerar fechar a conexão explicitamente aqui se necessário
        # await websocket.close(code=1011) # Internal Error
    finally:
        # Log para identificar QUAL conexão está entrando no finally
        print(f"[WebSocket Finally] Bloco finally executado para Cliente {client_id} (Lobby {lobby_id})") 
        manager.disconnect(websocket, lobby_id, client_id)
        
        lobby_was_found_and_player_removed = False
        new_host_assigned = False

        if lobby_id in lobbies:
            lobby = lobbies.get(lobby_id)
            if lobby and client_id in lobby.players:
                 # Guardar se o jogador que saiu era o host
                was_host = (lobby.host_client_id == client_id)
                
                del lobby.players[client_id]
                lobby_was_found_and_player_removed = True
                print(f"[WebSocket Finally] Jogador {client_id} removido do lobby {lobby_id}")

                if not lobby.players:
                    del lobbies[lobby_id]
                    print(f"[WebSocket Finally] Lobby {lobby_id} vazio e removido.")
                    # Não precisa notificar se o lobby foi deletado
                    return # Sair da função finally
                else:
                    # Se o host saiu e ainda há jogadores
                    if was_host:
                        # Designar o primeiro jogador restante como novo host
                        new_host_id = list(lobby.players.keys())[0]
                        lobby.host_client_id = new_host_id
                        new_host_assigned = True
                        print(f"[WebSocket Finally] Host {client_id} saiu. Novo host: {new_host_id} para lobby {lobby_id}.")
                        
                        # Se o jogo estava na seleção de música, o turno deve ser do novo host
                        if lobby.game_state == "song_selection":
                            lobby.turn_client_id = new_host_id
                            print(f"[WebSocket Finally] Estado era song_selection, turno atualizado para o novo host {new_host_id}.")
                        elif lobby.game_state not in ["results", "opponent_left"]:
                            # Se não está em resultados ou opponent_left, e o turno era do jogador que saiu
                            # e não é song_selection (já tratado), pode ser necessário ajustar o turno
                            # ou mudar o estado para opponent_left.
                            # Por enquanto, focamos em opponent_left.
                            pass


            # Se o lobby foi encontrado e o jogador removido (ou o lobby não foi encontrado, mas ainda tentamos notificar se há um lobby_id)
            # E se o lobby ainda existe após a potencial remoção de jogador/lobby vazio
            if lobby_id in lobbies:
                print(f"[WebSocket Finally] Agendando notificação para lobby {lobby_id} após desconexão/mudança de host.")
                asyncio.create_task(notify_lobby_update(lobby_id))
            elif lobby_was_found_and_player_removed and not (lobby_id in lobbies):
                print(f"[WebSocket Finally] Lobby {lobby_id} foi removido (ficou vazio), NENHUMA notificação será enviada.")
            else:
                print(f"[WebSocket Finally] Lobby {lobby_id} não encontrado ou não modificado de forma a necessitar notificação final (ex: já removido anteriormente).")

# <<< NOVA ROTA PARA UPLOAD DE ÁUDIO NO MODO SOLO >>>
@app.post("/solo/upload_audio/{song_id}")
async def upload_solo_audio_route(
    song_id: str, 
    audio_file: UploadFile = File(...)
):
    if not WHISPER_MODEL:
        print("Erro: Modelo Whisper não carregado. Upload de áudio solo falhou.")
        return JSONResponse(status_code=503, content={"detail": "Serviço de transcrição indisponível."})

    if song_id not in SONGS:
        return JSONResponse(status_code=404, content={"detail": "Música não encontrada."})

    song_info = SONGS[song_id]
    original_lyrics = ""

    # Tenta extrair a letra do LRC primeiro
    lrc_text = extract_text_from_lrc(song_id)
    if lrc_text:
        original_lyrics = lrc_text
        print(f"[Solo Upload] Letra extraída do LRC para {song_id}")
    elif song_info.get("letra"):
        original_lyrics = song_info["letra"]
        print(f"[Solo Upload] Usando letra do JSON para {song_id}")
    else:
        print(f"[Solo Upload] Letra não encontrada para {song_id}")
        return JSONResponse(status_code=404, content={"detail": "Letra da música não encontrada."})

    # Salvar arquivo de áudio temporariamente
    temp_audio_path = None
    try:
        # Garante que o diretório TEMP_DIR exista
        TEMP_DIR.mkdir(parents=True, exist_ok=True)
        
        fd, temp_audio_path_str = tempfile.mkstemp(suffix=".webm", dir=TEMP_DIR)
        temp_audio_path = Path(temp_audio_path_str)
        os.close(fd) # Fecha o descritor de arquivo para que aiofiles possa abri-lo

        async with aiofiles.open(temp_audio_path, "wb") as out_file:
            content = await audio_file.read()
            await out_file.write(content)
        print(f"[Solo Upload] Áudio salvo temporariamente em: {temp_audio_path}")

        # Transcrever áudio
        print(f"[Solo Upload] Iniciando transcrição para {temp_audio_path}")
        transcription = await transcribe_audio(str(temp_audio_path))
        print(f"[Solo Upload] Transcrição recebida: '{transcription[:50]}...' para {song_id}")

        # Calcular pontuação
        score = calculate_score(original_lyrics, transcription)
        print(f"[Solo Upload] Pontuação calculada: {score}% para {song_id}")

        return {
            "song_id": song_id,
            "transcription": transcription,
            "original_lyrics": original_lyrics, # Envia a letra original usada para pontuação
            "score": score
        }
    except Exception as e:
        print(f"Erro durante o processamento do áudio solo para {song_id}: {e}")
        # Adicionar mais detalhes do erro no log ou resposta, se apropriado
        return JSONResponse(status_code=500, content={"detail": f"Erro ao processar áudio: {str(e)}"})
    finally:
        if temp_audio_path and temp_audio_path.exists():
            try:
                os.remove(temp_audio_path)
                print(f"[Solo Upload] Arquivo de áudio temporário removido: {temp_audio_path}")
            except Exception as e:
                print(f"Erro ao remover arquivo de áudio temporário {temp_audio_path}: {e}")

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    # Passar SONGS para o template é crucial para a lista de músicas no frontend
    return templates.TemplateResponse("index.html", {"request": request, "songs": SONGS})

# O endpoint /record não é mais necessário da forma antiga, pois o áudio será enviado via WebSocket.
# O endpoint /song/{song_id} pode ser mantido se o cliente precisar buscar detalhes da música separadamente,
# mas na nova lógica, a letra já está no objeto Lobby quando a música é selecionada.

if __name__ == "__main__":
    # Passa o objeto 'app' diretamente para uvicorn.run
    # reload=False é o padrão neste modo, então não precisamos especificar
    uvicorn.run(app, host="0.0.0.0", port=8000) 