:root {
    /* <PERSON><PERSON><PERSON>/Imagem */
    --bg-color: #0D1117;
    --card-bg-color: #1C2128;
    --input-bg-color: #2D333B;
    --primary-text-color: #E6EDF3; /* Branco/Cinza muito claro para texto principal */
    --secondary-text-color: #7D8590; /* Cinza para texto secundário/descrições */
    --accent-green: #28A745; /* Verde para o botão Criar Lobby */
    --accent-blue: #007BFF; /* Azul para o botão Entrar no Lobby */
    --accent-purple: #A371F7; /* Roxo/Magenta para o ícone de microfone */
    --border-radius-md: 12px;
    --border-radius-sm: 8px;
    --font-family-apple: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";

    /* <PERSON><PERSON> antigas (manter se alguma parte ainda usar, ou remover depois) */
    --discord-dark: #2c2f33; --discord-darker: #23272a; --discord-darkest: #1e2124;
    --discord-light-gray: #99aab5; --discord-gray: #72767d; --discord-blurple: #5865F2;
    --discord-green: #57F287; --discord-red: #ED4245; --discord-white: #ffffff;
    --discord-text: #dcddde; 
    --discord-blurple-rgb: 88, 101, 242;
}

/* Remover as fontes Whitney antigas, a menos que especificamente necessárias em outro lugar */
/* @font-face { font-family: 'Whitney'; src: url('https://cdn.jsdelivr.net/gh/mahmoudftw/Whitney-Font@master/Whitney-Book.woff') format('woff'); font-weight: 400; } */
/* @font-face { font-family: 'Whitney'; src: url('https://cdn.jsdelivr.net/gh/mahmoudftw/Whitney-Font@master/Whitney-Medium.woff') format('woff'); font-weight: 500; } */
/* @font-face { font-family: 'Whitney'; src: url('https://cdn.jsdelivr.net/gh/mahmoudftw/Whitney-Font@master/Whitney-Semibold.woff') format('woff'); font-weight: 600; } */
/* @font-face { font-family: 'Whitney'; src: url('https://cdn.jsdelivr.net/gh/mahmoudftw/Whitney-Font@master/Whitney-Bold.woff') format('woff'); font-weight: 700; } */

body {
    font-family: var(--font-family-apple);
    background-color: var(--bg-color);
    color: var(--primary-text-color);
    padding-top: 20px; /* Pode ser ajustado/removido */
    padding-bottom: 20px; /* Pode ser ajustado/removido */
    display: flex;
    flex-direction: column;
    align-items: center; /* Centralizar conteúdo principal */
    min-height: 100vh;
    margin: 0;
    padding: 20px; /* Adiciona um padding geral ao body */
    box-sizing: border-box;
    background-image: none !important; /* Remover qualquer imagem de fundo */
    position: relative; /* Para posicionamento correto */
}

.container-main { 
    flex-grow: 1; 
    width: 100%;
    max-width: 960px; /* Limitar a largura máxima para melhor leitura em telas grandes */
    padding: 0; /* Removido padding daqui, já está no body */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center; /* Centraliza o conteúdo dentro do container */
    background-color: transparent !important; /* Garantir que não tenha fundo */
    border: none !important; /* Remover qualquer borda */
    box-shadow: none !important; /* Remover qualquer sombra */
    border-radius: 0 !important; /* Remover qualquer arredondamento */
}

h1, h2, h3, h4, h5, h6 {
    color: var(--primary-text-color);
    font-weight: 600;
    margin-top: 0; /* Remove margem superior padrão */
}

/* Estilos do Header (Karaokê das Irmãs) */
header {
    width: 100%;
    margin-bottom: 40px; /* Aumenta o espaço abaixo do header */
}
header .d-flex {
    justify-content: flex-start !important; /* Alinha à esquerda como na imagem */
}
header .d-flex h1 {
    font-size: 1.5rem; /* Ajustado para imagem */
    font-weight: 500;
}

header .header-icon {
    font-size: 1.5rem;
    margin-right: 10px;
    color: var(--accent-purple); 
}

/* Título Principal (Bem-vindo ao Karaokê!) */
.main-title {
    font-size: 2.8rem; /* Ajustado */
    font-weight: 700;
    color: var(--primary-text-color);
    text-align: center;
    margin-bottom: 10px;
}

/* Subtítulo */
.main-subtitle {
    font-size: 1.1rem; /* Ajustado */
    color: var(--secondary-text-color);
    text-align: center;
    margin-bottom: 50px;
}

footer.site-footer {
    text-align: center;
    padding: 30px 0 10px 0; /* Menor padding inferior */
    color: var(--secondary-text-color);
    font-size: 0.85em;
    margin-top: auto;
    width: 100%;
}

/* Wrapper para o conteúdo principal da página de boas-vindas */
.main-content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    background-color: transparent !important; /* Garantir que não tenha fundo */
    border: none !important; /* Remover qualquer borda */
    box-shadow: none !important; /* Remover qualquer sombra */
    padding: 0 !important; /* Remover qualquer padding interno */
    margin: 0 !important; /* Remover qualquer margem */
}

/* Card de Configuração Inicial de Nome */
.setup-card {
    background-color: transparent; /* Fundo transparente para esta seção */
    padding: 20px 0; /* Ajustado padding */
    border-radius: var(--border-radius-md);
    text-align: center;
    margin-bottom: 30px;
    width: 100%;
    max-width: 450px;
    box-shadow: none; /* Sem sombra para este card específico */
}

.setup-card-title {
    font-size: 1.2rem; /* Menor que os títulos dos cards de lobby */
    margin-bottom: 20px;
    color: var(--primary-text-color);
    font-weight: 500;
}

.setup-card .form-control-lg {
    background-color: var(--input-bg-color);
    border: 1px solid #30363D;
    color: var(--primary-text-color);
    border-radius: var(--border-radius-sm);
    padding: 12px 15px;
    font-size: 1rem;
    text-align: center; /* Centraliza placeholder */
}
.setup-card .form-control-lg::placeholder {
    color: var(--secondary-text-color);
}
.setup-card .form-control-lg:focus {
    background-color: var(--input-bg-color);
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    color: var(--primary-text-color);
}

.setup-card .btn-primary {
    background-color: var(--accent-blue);
    border-color: var(--accent-blue);
    color: var(--discord-white);
    padding: 10px 25px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: var(--border-radius-sm);
    margin-top: 15px;
}
.setup-card .btn-primary:hover {
    filter: brightness(1.15);
}

/* Container para os cards de Criar/Entrar no Lobby */
.lobby-options-container {
    display: flex;
    justify-content: center;
    gap: 24px; /* Espaço entre os cards */
    flex-wrap: wrap;
    width: 100%;
    max-width: 820px; 
    margin-bottom: 40px; /* Espaço abaixo dos cards */
    background-color: transparent !important; /* Garantir que não tenha fundo */
    padding: 0; /* Remover qualquer padding */
    border: none !important; /* Remover qualquer borda */
    box-shadow: none !important; /* Remover qualquer sombra */
}

.lobby-option-card {
    background-color: var(--card-bg-color);
    padding: 30px;
    border-radius: var(--border-radius-md);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 380px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    border: none; /* Sem bordas para estilo Apple limpo */
}

.lobby-card-icon-wrapper {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.lobby-card-icon-wrapper.icon-create {
    background-color: var(--accent-green);
    color: var(--discord-white);
}

.lobby-card-icon-wrapper.icon-join {
    background-color: var(--accent-blue);
    color: var(--discord-white);
}

.lobby-card-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--primary-text-color);
    margin-bottom: 8px;
}

.lobby-card-description {
    font-size: 0.9rem;
    color: var(--secondary-text-color);
    margin-bottom: 25px;
    line-height: 1.5;
    min-height: 60px; /* Para alinhar botões caso descrições tenham tamanhos diferentes */
}

.lobby-option-card .action-button {
    padding: 10px 15px;
    font-size: 0.95rem;
    font-weight: 500;
    border-radius: var(--border-radius-sm);
    width: 100%;
    max-width: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.lobby-option-card .action-button i {
    margin-right: 8px;
    font-size: 0.9em;
}

.lobby-option-card .btn-success {
    background-color: var(--accent-green);
    border-color: var(--accent-green);
    color: var(--discord-white);
}

.lobby-option-card .btn-primary {
    background-color: var(--accent-blue);
    border-color: var(--accent-blue);
    color: var(--discord-white);
}

.lobby-id-input {
    background-color: var(--input-bg-color);
    border: 1px solid #30363D;
    color: var(--primary-text-color);
    border-radius: var(--border-radius-sm);
    padding: 10px 15px;
    font-size: 0.95rem;
    text-align: center;
    width: 100%;
    max-width: 280px;
    margin-bottom: 15px; /* Espaço antes do botão Entrar */
}
.lobby-id-input::placeholder {
    color: var(--secondary-text-color);
}
.lobby-id-input:focus {
    background-color: var(--input-bg-color);
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    color: var(--primary-text-color);
}

/* Estilos de .card genérico para outras seções (jogo, resultados, etc.) */
.card {
    background-color: var(--card-bg-color);
    border: 1px solid #2A2F38; /* Borda sutil para cards */
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    margin-bottom: 25px;
}

.card-header {
    background-color: #161B22; /* Um pouco mais escuro que o card-bg */
    color: var(--primary-text-color);
    border-bottom: 1px solid #2A2F38;
    font-weight: 500;
    padding: 0.8rem 1.25rem;
    border-top-left-radius: var(--border-radius-md); /* Arredondamento consistente */
    border-top-right-radius: var(--border-radius-md);
    display: flex; /* Para alinhar ícone e texto */
    align-items: center;
}
.card-header i.fas {
    margin-right: 10px;
    font-size: 1em;
}

.card-body {
    padding: 1.25rem;
}

/* Seção do Jogo - Header do Lobby */
#game-section .card-header span {
    font-weight: 500;
}

#lobbyIdDisplay {
    background-color: var(--input-bg-color); 
    padding: 6px 12px; 
    border-radius: var(--border-radius-sm); 
    font-weight: bold;
    color: var(--primary-text-color);
    font-size: 0.9rem;
    border: 1px solid #30363D;
}

#leaveLobbyBtn {
    background-color: var(--discord-red);
    border-color: var(--discord-red);
    color: var(--discord-white);
    padding: 0.3rem 0.8rem;
    font-size: 0.85rem;
}
#leaveLobbyBtn:hover {
    filter: brightness(1.1);
}

/* Cards dos Jogadores na Game Section */
#game-section .player-card {
    border: 2px solid var(--input-bg-color); /* Borda padrão mais escura */
    padding: 1rem;
    border-radius: var(--border-radius-md); /* Bordas mais arredondadas */
    margin-bottom: 1rem;
    background-color: var(--card-bg-color);
    min-height: 100px; /* Altura mínima para consistência */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

#game-section .player-card h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
}
#game-section .player-card h5 i {
    margin-right: 8px;
    font-size: 0.9em;
}

#game-section .player-card small {
    font-size: 0.85rem;
    color: var(--secondary-text-color);
}

/* Cores específicas para os cards dos jogadores */
#player1Display.border-success,
#game-section #player1Display.active-turn { /* Para o jogador local ativo */
    border-color: var(--accent-green) !important;
    box-shadow: 0 0 8px rgba(var(--accent-green), 0.4) !important;
}

#player2Display.border-primary,
#game-section #player2Display.active-turn { /* Para o oponente ativo */
    border-color: var(--accent-blue) !important;
    box-shadow: 0 0 8px rgba(var(--accent-blue), 0.4) !important;
}

/* Switch de Dueto */
#game-section .form-check-input {
    width: 3em;
    height: 1.5em;
    margin-top: 0.2em;
    background-color: var(--input-bg-color);
    border-color: #4A5058;
}
#game-section .form-check-input:checked {
    background-color: var(--accent-green);
    border-color: var(--accent-green);
}
#game-section .form-check-label {
    color: var(--primary-text-color);
    padding-left: 0.5em;
}
#game-section .badge {
    vertical-align: middle;
}

/* Mensagem de Status do Lobby */
#lobbyStatusMessage {
    background-color: var(--input-bg-color);
    border: 1px solid #30363D;
    color: var(--primary-text-color);
    padding: 0.8rem 1rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.95rem;
}
#lobbyStatusMessage.alert-info {
    border-left: 4px solid var(--accent-blue);
}
#lobbyStatusMessage.alert-primary { /* Host selecionando música */
    border-left: 4px solid var(--accent-purple); /* Roxo para diferenciar */
}
#lobbyStatusMessage.alert-warning { /* Vez de alguém */
    border-left: 4px solid #FFA500; /* Laranja */
}
#lobbyStatusMessage.alert-success { /* Duelo finalizado */
    border-left: 4px solid var(--accent-green);
}
#lobbyStatusMessage.alert-danger { /* Erro ou oponente saiu */
    border-left: 4px solid var(--discord-red);
}


/* Área de Seleção de Música */
#song-selection-area .card-header {
    font-size: 1.1rem; /* Aumentar um pouco */
}

#songList {
    max-height: 50vh; /* Aumentar altura máxima para ver mais músicas */
    overflow-y: auto;
    padding-right: 5px; /* Espaço para a scrollbar não sobrepor conteúdo */
}

#songList .list-group-item {
    background-color: transparent;
    border: none; /* Remover borda padrão do item */
    border-bottom: 1px solid #2A2F38; /* Linha divisória sutil */
    color: var(--primary-text-color);
    border-radius: 0; /* Sem arredondamento individual */
    padding: 0.9rem 0.5rem; /* Ajustar padding */
}
#songList .list-group-item:last-child {
    border-bottom: none; /* Remover última borda */
}

#songList .list-group-item:hover, 
#songList .list-group-item.active {
    background-color: rgba(var(--accent-blue-rgb, 0, 123, 255), 0.15); /* Azul bem sutil no hover/active */
    color: var(--primary-text-color); /* Manter cor do texto */
}

#songList .list-group-item h5 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.2rem;
    color: var(--primary-text-color);
}
#songList .list-group-item p {
    font-size: 0.8rem;
    color: var(--secondary-text-color);
    margin-bottom: 0;
}

/* Estilização da Barra de Rolagem para a lista de músicas */
#songList::-webkit-scrollbar {
    width: 8px;
}
#songList::-webkit-scrollbar-track {
    background: var(--input-bg-color); 
    border-radius: 4px;
}
#songList::-webkit-scrollbar-thumb {
    background: var(--secondary-text-color); 
    border-radius: 4px;
}
#songList::-webkit-scrollbar-thumb:hover {
    background: #90959D; /* Cor um pouco mais clara no hover */
}

.list-group-item {
    background-color: transparent; /* Fundo transparente dentro do card */
    border: 1px solid #30363D;
    color: var(--primary-text-color);
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
    cursor: pointer;
    margin-bottom: 6px;
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
}
.list-group-item:hover, .list-group-item.active {
    background-color: var(--accent-blue);
    color: var(--discord-white);
    border-color: var(--accent-blue);
}
.list-group-item.disabled {
    cursor: not-allowed;
    background-color: var(--input-bg-color);
    opacity: 0.5;
}
.list-group-item h5 {
    color: var(--primary-text-color);
    margin-bottom: 0.3rem; font-size: 1rem;
}
.list-group-item p {
    color: var(--secondary-text-color);
    font-size: 0.85rem; margin-bottom: 0;
}

/* Ajustes gerais para botões */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.6rem 1.2rem; /* Ajuste de padding geral */
    transition: filter 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    border: 1px solid transparent;
}
.btn:hover:not(:disabled) {
    filter: brightness(1.1);
}
.btn:focus {
    box-shadow: 0 0 0 3px rgba(var(--accent-blue), 0.35); /* Foco mais sutil */
}

.btn-danger { 
    background-color: var(--discord-red); 
    border-color: var(--discord-red); 
    color: var(--discord-white); 
}
.btn-success {
    background-color: var(--accent-green);
    border-color: var(--accent-green);
    color: var(--discord-white);
}
.btn-primary {
    background-color: var(--accent-blue);
    border-color: var(--accent-blue);
    color: var(--discord-white);
}
.btn-info {
    background-color: #17A2B8; /* Cor Bootstrap info, ajustar se necessário */
    border-color: #17A2B8;
    color: var(--discord-white);
}
.btn-outline-danger {
    color: var(--discord-red);
    border-color: var(--discord-red);
}
.btn-outline-danger:hover {
    background-color: var(--discord-red);
    color: var(--discord-white);
}
.btn-outline-secondary {
    color: var(--secondary-text-color);
    border-color: var(--secondary-text-color);
}
.btn-outline-secondary:hover {
    background-color: var(--secondary-text-color);
    color: var(--primary-text-color);
}

.lyrics {
    white-space: pre-line;
    line-height: 1.6;
    font-size: 1rem;
    background-color: var(--input-bg-color);
    padding: 15px;
    border-radius: var(--border-radius-sm);
    max-height: 220px;
    overflow-y: auto;
    border: 1px solid #30363D;
    color: var(--primary-text-color);
}
.lyrics::-webkit-scrollbar { width: 8px; }
.lyrics::-webkit-scrollbar-track { background: var(--card-bg-color); border-radius: 5px; }
.lyrics::-webkit-scrollbar-thumb { background: var(--secondary-text-color); border-radius: 5px; }

.score {
    font-size: 2.2rem;
    font-weight: 700;
    text-align: center;
    margin: 10px 0;
    color: var(--accent-green);
}
.player-score-display { font-size: 1.8rem; }

.recording-indicator {
    width: 10px; height: 10px; border-radius: 50%;
    background-color: var(--discord-red);
    display: inline-block; margin-right: 6px;
    animation: pulse 1.5s infinite ease-in-out; vertical-align: middle;
}
@keyframes pulse { 0% { transform: scale(1); opacity: 1; } 50% { transform: scale(1.1); opacity: 0.7; } 100% { transform: scale(1); opacity: 1; } }

.spinner-border { 
    width: 2rem; 
    height: 2rem; 
    color: var(--accent-blue);
}

.form-control, .form-select {
    background-color: var(--input-bg-color);
    color: var(--primary-text-color);
    border: 1px solid #30363D;
    border-radius: var(--border-radius-sm);
    padding: 0.6rem 1rem;
}
.form-control:focus, .form-select:focus {
    background-color: var(--input-bg-color);
    color: var(--primary-text-color);
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}
.form-label {
    color: var(--secondary-text-color);
    margin-bottom: 0.4rem;
    font-size: 0.9rem;
}

.section { 
    display: none; 
    background-color: transparent !important; /* Garantir que a seção não tenha fundo */
    padding: 0; /* Remover padding que poderia criar espaço adicional */
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
}
.section.active { 
    display: block; 
}

.player-card {
    border: 1px solid #30363D;
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    margin-bottom:1rem;
    background-color: var(--card-bg-color);
}
.player-card.active-turn {
    border-color: var(--accent-blue);
    box-shadow: 0 0 8px rgba(var(--accent-blue), 0.4);
}

.lobby-id-display { 
    user-select: all; 
    cursor: pointer; 
    background-color: var(--input-bg-color); 
    padding: 6px 10px; 
    border-radius:var(--border-radius-sm); 
    font-weight:500; 
    color: var(--primary-text-color);
    font-size: 0.9rem;
}

.lyrics-line { 
    color: var(--secondary-text-color);
    padding: 2px 6px;
    margin-bottom: 3px;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.3s ease, color 0.3s ease;
}
.lyrics-line.current-line {
    background-color: var(--accent-blue);
    color: var(--discord-white);
    font-weight: 500;
}

#lyricsDisplay {
    padding: 5px;
}
#audioPlayerContainer {
     margin-bottom: 1.2rem; 
}
 
#results-area #resultOriginalLyrics p {
     color: var(--primary-text-color); 
     font-size: 0.9rem;
}

#audioLoadingIndicator .progress {
     margin-top: 0.75rem; 
}

#effectSelect:disabled {
     color: var(--secondary-text-color) !important; 
     background-color: var(--input-bg-color) !important; 
     opacity: 0.5; 
     -webkit-text-fill-color: var(--secondary-text-color);
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Estilos para o card do Modo Solo - CORRIGIDO */
.solo-mode-card {
    background-color: var(--card-bg-color) !important; /* Forçar mesma cor dos outros cards */
    border-left: none !important; /* Remover borda esquerda com !important */
    border: none; /* Remover todas as bordas */
    box-shadow: 0 5px 15px rgba(0,0,0,0.2); /* Adicionar a mesma sombra */
}

.icon-solo {
    background-color: var(--accent-blue); /* Mesma cor do ícone de "Entrar em Lobby" */
    color: var(--discord-white);
}

/* Garantir que não exista nenhum container ou elemento com fundo cinza */
.container, .container-fluid, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
}

/* Regra geral para eliminar fundos indesejados */
.container-main > *, .container-main > section > div:not(.lobby-option-card):not(.card) {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
    padding: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Ajuste para o lobby-actions-section para dar mais destaque aos cards */
#lobby-actions-section {
    width: 100%;
    padding: 0 !important;
    margin: 0 !important;
    background: transparent !important;
}