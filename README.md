# Karaokê em Duelo

Um aplicativo simples de karaokê que permite aos jogadores cantar, transcrever a voz e pontuar com base na letra original da música.

## Funcionalidades

- Interface web responsiva para seleção de músicas
- Gravação de áudio via microfone do navegador
- Transcrição de áudio usando Whisper local
- Pontuação baseada na correspondência entre a transcrição e a letra original
- Suporte para acessar via Tailscale em outros dispositivos

## Requisitos

- Python 3.8+
- PyTorch e Whisper (já incluídos nos requisitos)
- Navegador moderno com suporte a MediaRecorder API
- Tailscale (opcional, para acesso remoto)

## Instalação

1. Clone o repositório:
```
git clone <url_do_repositorio>
cd karaoke-duelo
```

2. Instale as dependências:
```
pip install -r requirements.txt
```

3. Execute o servidor:
```
python app.py
```

4. Acesse o aplicativo:
   - Localmente: http://localhost:8000
   - Via Tailscale: http://<seu_ip_tailscale>:8000

## Observações importantes

O aplicativo usa o modelo "base" do Whisper, que é mais leve e consome menos recursos. Se você tiver uma máquina potente e quiser melhor qualidade de transcrição, você pode editar o arquivo `app.py` e alterar:

```python
WHISPER_MODEL = whisper.load_model("base")
```

para:

```python
WHISPER_MODEL = whisper.load_model("medium")  # ou "large" se tiver >8GB de RAM
```

## Uso alternativo (OpenAI API)

Se preferir usar a API da OpenAI em vez do modelo local:

1. Edite `app.py` para comentar a parte que usa Whisper local e descomentar a parte que usa a API da OpenAI
2. Crie um arquivo `.env` na raiz do projeto e adicione: `OPENAI_API_KEY=sua_chave_aqui`
3. Atualize o requirements.txt para incluir `openai==0.28.0`

## Personalização

Para adicionar novas músicas, crie arquivos JSON no diretório `songs/` seguindo o formato:
```json
{
  "titulo": "Nome da Música",
  "artista": "Nome do Artista",
  "letra": "Letra da música aqui\nCom quebras de linha"
}
```

## Licença

Este projeto está licenciado sob a licença MIT. 