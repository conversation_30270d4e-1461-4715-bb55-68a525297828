<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> das Irmãs v2 👯‍♀️ 🎤</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
</head>
<body>
    <div class="container-main">
        <header class="text-center pt-4 mb-4">
            <div class="d-flex justify-content-center align-items-center mb-3">
                <i class="fas fa-microphone header-icon"></i>
                <h1 class="display-5">Karaokê das Irmãs</h1>
            </div>
        </header>

        <div class="main-content-wrapper">
            <h2 class="main-title">Bem-vindo ao Karaokê!</h2>
            <p class="main-subtitle">Cante suas músicas favoritas com seus amigos.</p>

            <!-- Seção de Configuração Inicial (mantida por enquanto, mas talvez movida ou reestilizada depois) -->
            <section id="initial-setup-section" class="section active">
                <div class="setup-card"> <!-- Nova classe para estilização do card de setup -->
                    <h3 class="setup-card-title"><i class="fas fa-user-plus me-2"></i>Defina seu nome:</h3>
                    <div class="mb-3 mx-auto" style="max-width: 350px;">
                        <label for="playerNameInput" class="form-label sr-only">Seu Nome de Cantor:</label> <!-- sr-only para acessibilidade se o label visual não for necessário -->
                        <input type="text" class="form-control form-control-lg" id="playerNameInput" placeholder="Seu Nome de Cantor">
                    </div>
                    <button id="setPlayerNameBtn" class="btn btn-primary btn-lg"><i class="fas fa-check me-2"></i>Confirmar Nome</button>
                </div>
            </section>

            <!-- Seção de Ações do Lobby (Criar/Entrar) -->
            <section id="lobby-actions-section" class="section">
                <div class="lobby-options-container">
                    <div class="lobby-option-card create-lobby-card">
                        <div class="lobby-card-icon-wrapper icon-create">
                            <i class="fas fa-plus"></i>
                        </div>
                        <h3 class="lobby-card-title">Criar Novo Lobby</h3>
                        <p class="lobby-card-description">Inicie uma nova sessão de karaokê e convide seus amigos para participar.</p>
                        <button id="createLobbyBtn" class="btn btn-success btn-lg action-button">
                            <i class="fas fa-arrow-right me-2"></i>Criar Lobby
                        </button>
                    </div>

                    <div class="lobby-option-card join-lobby-card">
                        <div class="lobby-card-icon-wrapper icon-join">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <h3 class="lobby-card-title">Entrar em Lobby</h3>
                        <p class="lobby-card-description">Já tem um código? Insira abaixo para entrar em uma sessão existente.</p>
                        <input type="text" class="form-control form-control-lg lobby-id-input" id="lobbyIdInput" placeholder="Insira o ID do Lobby">
                        <button id="joinLobbyBtn" class="btn btn-primary btn-lg action-button mt-3">
                            <i class="fas fa-arrow-right me-2"></i>Entrar
                        </button>
                    </div>

                    <!-- NOVO CARD PARA MODO SOLO -->
                    <div class="lobby-option-card solo-mode-card">
                        <div class="lobby-card-icon-wrapper icon-solo">
                            <i class="fas fa-user"></i>
                        </div>
                        <h3 class="lobby-card-title">Modo Solo</h3>
                        <p class="lobby-card-description">Cante suas músicas favoritas sozinho e veja sua pontuação.</p>
                        <button id="startSoloGameBtn" class="btn btn-info btn-lg action-button">
                            <i class="fas fa-play me-2"></i>Iniciar Jogo Solo
                        </button>
                    </div>
                </div>
            </section>
        </div>

        <!-- Seção Principal do Jogo (Lobby, Seleção de Música, Duelo) -->
        <section id="game-section" class="section">
            <div class="card shadow-sm mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <!-- Restaurado -->
                    <span>Lobby ID: <strong id="lobbyIdDisplay" class="lobby-id-display" title="Clique para copiar">N/A</strong></span>
                    <button id="leaveLobbyBtn" class="btn btn-sm btn-outline-danger"><i class="fas fa-sign-out-alt me-1"></i> Sair do Lobby</button>
                </div>
                <div class="card-body">
                    <div class="row mb-3"> <!-- Player cards row -->
                        <div class="col-md-6">
                            <div id="player1Display" class="player-card">
                                <h5 id="player1NameDisplay"><i class="fas fa-user me-2"></i>Jogador 1 (Você)</h5>
                                <small id="player1Status" style="color: var(--discord-light-gray);"></small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div id="player2Display" class="player-card">
                                <h5 id="player2NameDisplay"><i class="fas fa-user-slash me-2"></i>Aguardando Oponente...</h5>
                                <small id="player2Status" style="color: var(--discord-light-gray);"></small>
                            </div>
                        </div>
                    </div>
                    <!-- Div removida que continha lobby id e botão sair -->

                    <!-- <<< Checkbox Modo Dueto movido para cá >>> -->
                    <div class="mt-2 mb-3 text-center">
                        <div class="form-check form-switch d-inline-block">
                            <input class="form-check-input" type="checkbox" role="switch" id="duetModeCheckbox">
                            <label class="form-check-label" for="duetModeCheckbox" style="color: var(--discord-text);">Dueto Real (Simultâneo)</label> <!-- Cor do texto ajustada -->
                            <span class="badge bg-danger rounded-pill ms-1">Experimental</span>
                        </div>
                    </div>

                    <div id="lobbyStatusMessage" class="alert alert-info text-center" role="alert" style="background-color: var(--discord-darkest); border-color: var(--discord-blurple); color: var(--discord-text);">Aguardando jogadores...</div>
                </div>
            </div>

            <div id="song-selection-area" class="apple-style-container" style="display: none;">
                <!-- Removido card shadow-sm mb-3 e card-header original -->
                <div class="apple-style-list-header">
                    <span class="material-icons-outlined">music_note</span>
                    <h2>Selecione uma Música (Host)</h2>
                </div>
                <ul role="list" id="songList" class="song-list-scrollable-area">
                    <!-- Músicas serão populadas pelo JS como <li> -->
                </ul>
            </div>

            <div id="game-play-area" class="card shadow-sm mb-3" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 id="currentSongTitle" class="mb-0 d-inline">Música</h5>
                        <small id="currentSongArtist" class="ms-2"></small>
                    </div>
                    <div id="turnIndicator" class="fw-bold">Vez de: -</div>
                </div>
                <div class="card-body">
                    <div id="audioPlayerContainer" class="text-center">
                        <audio id="audioPlayer" controls style="width: 100%; max-width: 500px;">
                            Seu navegador não suporta o elemento de áudio.
                        </audio>
                    </div>

                    <h6 class="mb-2" style="color: var(--discord-light-gray);"><i class="fas fa-align-left me-2"></i>Letra:</h6>
                    <div id="lyricsDisplay" class="lyrics mb-3">
                        <!-- Linhas de letra serão inseridas aqui pelo JS -->
                    </div>
                    <!-- Visualizador de Ondas Sonoras -->
                    <div id="audioVisualizerContainer" class="mb-3" style="display: none;">
                        <div class="text-center mb-2">
                            <h6 style="color: var(--discord-light-gray);"><i class="fas fa-wave-square me-2"></i>Visualização de Áudio</h6>
                        </div>
                        <div class="audio-visualizer-wrapper">
                            <canvas id="audioVisualizer" width="600" height="150"></canvas>
                            <div class="visualizer-controls mt-2">
                                <button id="toggleVisualizerBtn" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye me-1"></i>Ocultar Visualizador
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="recordingControls" class="text-center">
                        <button id="recordBtn" class="btn btn-primary btn-lg me-2" disabled>
                            <i class="fas fa-play me-2"></i>Começar
                        </button>
                        <button id="stopBtn" class="btn btn-danger btn-lg" style="display: none;">
                            <span class="recording-indicator"></span><i class="fas fa-stop-circle me-2"></i>Parar Gravação
                        </button>

                        <!-- <<< Botão de Retorno (posição original) >>> -->
                        <button id="monitorBtn" class="btn btn-outline-secondary btn-lg ms-2" title="Ouvir retorno do microfone (use fones!)" disabled>
                            <i class="fas fa-ear-listen"></i> Ouvir Retorno
                        </button>

                        <!-- <<< Controles de Efeitos (ao lado do Retorno) >>> -->
                        <div class="form-check form-switch d-inline-block align-middle ms-3">
                            <input class="form-check-input" type="checkbox" role="switch" id="enableEffectsCheckbox" disabled>
                            <label class="form-check-label" for="enableEffectsCheckbox" style="color: var(--discord-light-gray);">Adicionar efeitos?</label>
                            <span class="badge bg-danger rounded-pill ms-1" style="font-size: 0.6em; vertical-align: middle;">Experimental</span>
                        </div>
                        <div id="effectsBox" class="d-inline-block align-middle ms-1" style="display: none; max-width: 180px;">
                            <select class="form-select form-select-sm" id="effectSelect" disabled>
                                <option value="none" selected>Nenhum</option>
                                <option value="delay">Eco (Delay)</option>
                                <option value="lowpass">Abafado (Filtro)</option>
                                <option value="highpass">Radiofônico (Filtro)</option>
                                <option value="distortion">Distorção Leve</option>
                            </select>
                        </div>
                        <!-- <<< Fim dos Controles de Efeitos >>> -->
                    </div>
                    <!-- MODIFICADO: Indicador de Carregamento com Porcentagem -->
                    <div id="audioLoadingIndicator" class="mt-3 text-center" style="display: none;">
                        <p id="uploadStatusMessage" class="mb-1" style="color: var(--discord-light-gray);">Processando áudio...</p>
                        <p id="uploadProgressPercentage" class="h3 fw-bold" style="color: var(--discord-white); margin-top: 5px;">0%</p>
                    </div>
                </div>
            </div>

            <div id="results-area" class="card shadow-sm" style="display: none;">
                <div class="card-header"><i class="fas fa-award me-2"></i>Resultado Final do Duelo</div>
                <div class="card-body">
                    <h4 id="duelWinner" class="text-center mb-3">Calculando Vencedor...</h4>
                    <div class="row text-center">
                        <div class="col-md-6 border-end border-secondary mb-3 mb-md-0">
                            <h5 id="resultPlayer1Name">Jogador 1</h5>
                            <div class="score player-score-display" id="resultPlayer1Score">0%</div>
                        </div>
                        <div class="col-md-6">
                            <h5 id="resultPlayer2Name">Jogador 2</h5>
                            <div class="score player-score-display" id="resultPlayer2Score">0%</div>
                        </div>
                    </div>
                    <hr style="border-color: var(--discord-gray); margin-top: 1rem; margin-bottom: 1rem;">
                    <h6><i class="fas fa-file-alt me-2"></i>Letra Original (Referência):</h6>
                    <div id="resultOriginalLyrics" class="lyrics mb-3" style="max-height:150px;">
                        <!-- Letra aqui pode ser a simples ou a LRC parseada para referência -->
                    </div>
                    <div class="text-center mt-3">
                        <button id="playAgainBtn" class="btn btn-success"><i class="fas fa-redo me-2"></i>Jogar Novamente (Mesma Música)</button>
                        <button id="newGameBtn" class="btn btn-info ms-2"><i class="fas fa-music me-2"></i>Nova Música (Novo Duelo)</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- NOVA SEÇÃO PARA O JOGO SOLO -->
        <section id="solo-game-section" class="section">
            <!-- Removido o .card e .card-header antigos daqui -->
            <!-- A estrutura apple-style-container será o novo "card" -->

            <div id="solo-song-selection-area" class="apple-style-container">
                <div class="apple-style-header">
                    <h1 class="apple-style-title">Modo Solo</h1>
                    <button id="exitSoloModeBtn" class="apple-style-button">
                        <span class="material-icons-outlined">close</span>
                        Sair do Modo Solo
                    </button>
                </div>
                <div class="apple-style-list-header">
                    <span class="material-icons-outlined">music_note</span>
                    <h2>Selecione uma Música</h2>
                </div>
                <ul role="list" id="soloSongList" class="song-list-scrollable-area">
                    <!-- Músicas serão populadas pelo JS como <li> -->
                </ul>
            </div>

            <div id="solo-game-play-area" style="display: none;" class="apple-style-container mt-4">
                <!-- Mantém o conteúdo interno, mas o container externo agora é apple-style -->
                <div class="apple-style-header">
                     <h5 id="soloCurrentSongTitle" class="apple-style-title mb-0">Música</h5>
                     <!-- Poderia ter um botão de voltar para seleção aqui, ou o artista abaixo -->
                </div>
                <small id="soloCurrentSongArtist" class="text-muted d-block mb-3" style="color: #aeaeb2;"></small>
                
                <div id="soloAudioPlayerContainer" class="text-center mb-3">
                    <audio id="soloAudioPlayer" controls style="width: 100%; max-width: 500px;">
                        Seu navegador não suporta o elemento de áudio.
                    </audio>
                </div>

                <h6 class="mb-2" style="color: var(--discord-light-gray);"><i class="fas fa-align-left me-2"></i>Letra:</h6>
                <div id="soloLyricsDisplay" class="lyrics mb-3" style="max-height: 200px; overflow-y: auto;">
                    <!-- Linhas de letra serão inseridas aqui pelo JS -->
                </div>
                <!-- Visualizador de Ondas Sonoras para Modo Solo -->
                <div id="soloAudioVisualizerContainer" class="mb-3" style="display: none;">
                    <div class="text-center mb-2">
                        <h6 style="color: var(--discord-light-gray);"><i class="fas fa-wave-square me-2"></i>Visualização de Áudio</h6>
                    </div>
                    <div class="audio-visualizer-wrapper">
                        <canvas id="soloAudioVisualizer" width="600" height="150"></canvas>
                        <div class="visualizer-controls mt-2">
                            <button id="soloToggleVisualizerBtn" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-eye me-1"></i>Ocultar Visualizador
                            </button>
                        </div>
                    </div>
                </div>

                <div id="soloRecordingControls" class="text-center">
                    <button id="soloRecordBtn" class="btn btn-primary btn-lg me-2" disabled>
                        <i class="fas fa-play me-2"></i>Começar a Cantar
                    </button>
                    <button id="soloStopBtn" class="btn btn-danger btn-lg" style="display: none;">
                        <span class="recording-indicator"></span><i class="fas fa-stop-circle me-2"></i>Parar Gravação
                    </button>
                    <button id="soloMonitorBtn" class="btn btn-outline-secondary btn-lg ms-2" title="Ouvir retorno do microfone (use fones!)" disabled>
                        <i class="fas fa-ear-listen"></i> Ouvir Retorno
                    </button>
                </div>
                <div id="soloAudioLoadingIndicator" class="mt-3 text-center" style="display: none;">
                    <p id="soloUploadStatusMessage" class="mb-1" style="color: var(--discord-light-gray);">Processando áudio...</p>
                    <p id="soloUploadProgressPercentage" class="h3 fw-bold" style="color: var(--discord-white); margin-top: 5px;">0%</p>
                </div>
            </div>

            <div id="solo-results-area" class="mt-4" style="display: none;">
                <h4 class="text-center mb-3"><i class="fas fa-award me-2"></i>Sua Pontuação</h4>
                <div class="text-center">
                    <div class="score player-score-display" id="soloScoreDisplay" style="font-size: 2.5rem;">0%</div>
                </div>
                <hr style="border-color: var(--discord-gray); margin-top: 1rem; margin-bottom: 1rem;">
                <h6><i class="fas fa-file-alt me-2"></i>Letra Original (Referência):</h6>
                <div id="soloResultOriginalLyrics" class="lyrics mb-3" style="max-height:150px; overflow-y: auto;">
                    <!-- Letra aqui -->
                </div>
                <div class="text-center mt-3">
                    <button id="soloPlayAgainBtn" class="btn btn-success"><i class="fas fa-redo me-2"></i>Cantar Novamente</button>
                    <button id="soloNewSongBtn" class="btn btn-info ms-2"><i class="fas fa-music me-2"></i>Escolher Outra Música</button>
                </div>
            </div>
        </section>
    </div>

    <footer class="mt-auto site-footer">
        © 2023 Karaokê das Irmãs. Todos os direitos reservados.
    </footer>

    <script>
        // Determinar protocolo WebSocket com base no protocolo da página
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const WS_URL = `${protocol}//${window.location.host}/ws`;
        // const WS_URL = `ws://${window.location.host}/ws`; // Linha original comentada

        let websocket = null;
        let clientId = null;
        let lobbyId = null;
        let playerName = localStorage.getItem('karaokePlayerName') || "";
        let songsData = {}; // Inicializa vazio, será preenchido via fetch
        let currentLobbyState = {};
        let lrcFetchController = null; // <<< Para cancelar fetch anterior
        let isHost = false; // <<< Adicionar flag para saber se o cliente é o host

        // Variáveis de áudio
        let mediaRecorder = null; // Usado para modo REVEZAMENTO e potencialmente por lógica antiga
        let audioChunks = [];     // Usado para modo REVEZAMENTO
        let microphoneStream = null; // Stream GLOBAL, principalmente para MONITORAMENTO
        let audioContext = null;        // Contexto Web Audio API
        let microphoneSourceNode = null; // Nó de entrada do microfone para monitoramento
        let monitorGainNode = null;      // Nó de ganho para controlar volume do monitoramento (opcional)
        let isMonitoring = false;      // Estado do monitoramento

        // <<< NOVAS VARIÁVEIS GLOBAIS PARA GRAVAÇÃO DE DUETO ISOLADA >>>
        let myActiveDuetMediaRecorder = null;
        let myActiveDuetStream = null;
        // let myActiveDuetAudioChunks = []; // Os chunks já são locais em setupAndStartRecordingDuet

        // <<< Variáveis para Efeitos >>>
        let effectsEnabled = false;    // Se a checkbox está marcada
        let selectedEffect = 'none';   // Qual efeito está selecionado
        let delayNode = null;          // Nó para o efeito de Delay
        let filterNode = null;         // <<< Nó para Filtros (Lowpass/Highpass)
        let distortionNode = null;     // <<< Nó para Distorção

        // <<< VARIÁVEIS PARA TIMER DO INDICADOR DE CARREGAMENTO >>>
        let myAudioSubmissionTimestamp = 0;    // Timestamp de quando este cliente enviou áudio
        let otherPlayerAudioSubmissionTimestamp = 0; // Timestamp de quando o outro jogador enviou áudio
        const MIN_LOADING_INDICATOR_DURATION = 10000; // Tempo mínimo em ms (10 segundos) para mostrar o indicador

        // <<< VARIÁVEIS PARA VISUALIZADOR DE ÁUDIO >>>
        let audioVisualizer = null;           // Canvas para visualização multiplayer
        let soloAudioVisualizer = null;       // Canvas para visualização solo
        let visualizerContext = null;         // Contexto 2D do canvas multiplayer
        let soloVisualizerContext = null;     // Contexto 2D do canvas solo
        let analyserNode = null;              // Nó analisador para multiplayer
        let soloAnalyserNode = null;          // Nó analisador para solo
        let visualizerAnimationId = null;     // ID da animação multiplayer
        let soloVisualizerAnimationId = null; // ID da animação solo
        let isVisualizerActive = false;       // Estado do visualizador multiplayer
        let isSoloVisualizerActive = false;   // Estado do visualizador solo

        // Elementos da UI
        const sections = {
            initialSetup: document.getElementById('initial-setup-section'),
            lobbyActions: document.getElementById('lobby-actions-section'),
            game: document.getElementById('game-section'),
            soloGame: document.getElementById('solo-game-section') // <<< NOVA SEÇÃO SOLO
        };
        const playerNameInput = document.getElementById('playerNameInput');
        const setPlayerNameBtn = document.getElementById('setPlayerNameBtn');
        const createLobbyBtn = document.getElementById('createLobbyBtn');
        const lobbyIdInput = document.getElementById('lobbyIdInput');
        const joinLobbyBtn = document.getElementById('joinLobbyBtn');
        const lobbyIdDisplay = document.getElementById('lobbyIdDisplay');
        const leaveLobbyBtn = document.getElementById('leaveLobbyBtn');
        const player1NameDisplay = document.getElementById('player1NameDisplay');
        const player1Status = document.getElementById('player1Status');
        const player2NameDisplay = document.getElementById('player2NameDisplay');
        const player2Status = document.getElementById('player2Status');
        const player1DisplayCard = document.getElementById('player1Display');
        const player2DisplayCard = document.getElementById('player2Display');
        const lobbyStatusMessage = document.getElementById('lobbyStatusMessage');
        const songSelectionArea = document.getElementById('song-selection-area');
        const songListDiv = document.getElementById('songList');
        const gamePlayArea = document.getElementById('game-play-area');
        const currentSongTitle = document.getElementById('currentSongTitle');
        const currentSongArtist = document.getElementById('currentSongArtist');
        const turnIndicator = document.getElementById('turnIndicator');
        const lyricsDisplay = document.getElementById('lyricsDisplay');
        const recordingControls = document.getElementById('recordingControls');
        const recordBtn = document.getElementById('recordBtn');
        const stopBtn = document.getElementById('stopBtn');
        const audioLoadingIndicator = document.getElementById('audioLoadingIndicator');
        const resultsArea = document.getElementById('results-area');
        const duelWinner = document.getElementById('duelWinner');
        const resultPlayer1Name = document.getElementById('resultPlayer1Name');
        const resultPlayer1Score = document.getElementById('resultPlayer1Score');
        const resultPlayer2Name = document.getElementById('resultPlayer2Name');
        const resultPlayer2Score = document.getElementById('resultPlayer2Score');
        const resultOriginalLyrics = document.getElementById('resultOriginalLyrics');
        const playAgainBtn = document.getElementById('playAgainBtn');
        const newGameBtn = document.getElementById('newGameBtn');
        const audioPlayer = document.getElementById('audioPlayer');
        const monitorBtn = document.getElementById('monitorBtn');
        const enableEffectsCheckbox = document.getElementById('enableEffectsCheckbox'); // <<< Checkbox de efeitos
        const effectsBox = document.getElementById('effectsBox');               // <<< Div da seleção de efeitos
        const effectSelect = document.getElementById('effectSelect');             // <<< Select de efeitos
        const duetModeCheckbox = document.getElementById('duetModeCheckbox');       // <<< Checkbox de modo dueto

        // <<< Elementos do Visualizador de Áudio >>>
        const audioVisualizerContainer = document.getElementById('audioVisualizerContainer');
        const toggleVisualizerBtn = document.getElementById('toggleVisualizerBtn');
        const soloAudioVisualizerContainer = document.getElementById('soloAudioVisualizerContainer');
        const soloToggleVisualizerBtn = document.getElementById('soloToggleVisualizerBtn');

        // <<< Elementos da UI para Modo Solo >>>
        const startSoloGameBtn = document.getElementById('startSoloGameBtn');
        const exitSoloModeBtn = document.getElementById('exitSoloModeBtn');
        const soloSongSelectionArea = document.getElementById('solo-song-selection-area');
        const soloSongList = document.getElementById('soloSongList');
        const soloGamePlayArea = document.getElementById('solo-game-play-area');
        const soloCurrentSongTitle = document.getElementById('soloCurrentSongTitle');
        const soloCurrentSongArtist = document.getElementById('soloCurrentSongArtist');
        const soloAudioPlayer = document.getElementById('soloAudioPlayer');
        const soloLyricsDisplay = document.getElementById('soloLyricsDisplay');
        const soloRecordBtn = document.getElementById('soloRecordBtn');
        const soloStopBtn = document.getElementById('soloStopBtn');
        const soloMonitorBtn = document.getElementById('soloMonitorBtn');
        const soloAudioLoadingIndicator = document.getElementById('soloAudioLoadingIndicator');
        const soloUploadStatusMessage = document.getElementById('soloUploadStatusMessage');
        const soloUploadProgressPercentage = document.getElementById('soloUploadProgressPercentage');
        const soloResultsArea = document.getElementById('solo-results-area');
        const soloScoreDisplay = document.getElementById('soloScoreDisplay');
        const soloResultOriginalLyrics = document.getElementById('soloResultOriginalLyrics');
        const soloPlayAgainBtn = document.getElementById('soloPlayAgainBtn');
        const soloNewSongBtn = document.getElementById('soloNewSongBtn');

        // <<< Variáveis de estado para Modo Solo >>>
        let currentSoloSongId = null;
        let soloMediaRecorder = null;
        let soloAudioChunks = [];
        let soloLrcParser = null; // Para parsear LRC no modo solo
        let soloAudioContext = null;
        let soloMicrophoneSourceNode = null;
        let soloMonitorGainNode = null;
        let isSoloMonitoring = false;

        // Para o novo indicador de progresso percentual
        const uploadStatusMessage = document.getElementById('uploadStatusMessage');
        const uploadProgressPercentage = document.getElementById('uploadProgressPercentage');

        // <<< NOVA FUNÇÃO PARA GARANTIR QUE SONGS DATA ESTEJA CARREGADO >>>
        async function ensureSongsDataLoaded() {
            if (Object.keys(songsData).length === 0) {
                try {
                    console.log("[Songs Data] songsData está vazio. Buscando músicas...");
                    const response = await fetch('/api/songs');
                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Falha ao buscar músicas: ${response.status} ${errorText}`);
                    }
                    songsData = await response.json();
                    console.log("[Songs Data] Músicas carregadas em songsData:", songsData);
                    if (Object.keys(songsData).length === 0) {
                        console.warn("[Songs Data] Músicas buscadas, mas songsData ainda está vazio. O servidor retornou uma lista vazia?");
                    }
                } catch (error) {
                    console.error("Erro crítico ao buscar songsData:", error);
                    throw error; // Lançar o erro para que a função chamadora possa tratar a UI
                }
            }
        }

        // <<< FUNÇÕES DO VISUALIZADOR DE ÁUDIO >>>
        function initializeAudioVisualizer() {
            console.log('[Visualizer] Inicializando visualizador de áudio...');

            audioVisualizer = document.getElementById('audioVisualizer');
            soloAudioVisualizer = document.getElementById('soloAudioVisualizer');

            console.log('[Visualizer] Elementos encontrados:', {
                audioVisualizer: !!audioVisualizer,
                soloAudioVisualizer: !!soloAudioVisualizer
            });

            if (audioVisualizer) {
                visualizerContext = audioVisualizer.getContext('2d');
                // Ajustar canvas para alta resolução
                const rect = audioVisualizer.getBoundingClientRect();
                console.log('[Visualizer] Configurando canvas multiplayer:', rect);

                // Definir tamanho mínimo se o elemento não estiver visível
                const width = rect.width > 0 ? rect.width : 600;
                const height = rect.height > 0 ? rect.height : 150;

                audioVisualizer.width = width * window.devicePixelRatio;
                audioVisualizer.height = height * window.devicePixelRatio;
                visualizerContext.scale(window.devicePixelRatio, window.devicePixelRatio);
                audioVisualizer.style.width = width + 'px';
                audioVisualizer.style.height = height + 'px';

                console.log('[Visualizer] Canvas multiplayer configurado:', {
                    width: audioVisualizer.width,
                    height: audioVisualizer.height,
                    styleWidth: audioVisualizer.style.width,
                    styleHeight: audioVisualizer.style.height
                });
            }

            if (soloAudioVisualizer) {
                soloVisualizerContext = soloAudioVisualizer.getContext('2d');
                // Ajustar canvas para alta resolução
                const rect = soloAudioVisualizer.getBoundingClientRect();
                console.log('[Visualizer] Configurando canvas solo:', rect);

                // Definir tamanho mínimo se o elemento não estiver visível
                const width = rect.width > 0 ? rect.width : 600;
                const height = rect.height > 0 ? rect.height : 150;

                soloAudioVisualizer.width = width * window.devicePixelRatio;
                soloAudioVisualizer.height = height * window.devicePixelRatio;
                soloVisualizerContext.scale(window.devicePixelRatio, window.devicePixelRatio);
                soloAudioVisualizer.style.width = width + 'px';
                soloAudioVisualizer.style.height = height + 'px';

                console.log('[Visualizer] Canvas solo configurado:', {
                    width: soloAudioVisualizer.width,
                    height: soloAudioVisualizer.height,
                    styleWidth: soloAudioVisualizer.style.width,
                    styleHeight: soloAudioVisualizer.style.height
                });
            }

            console.log('[Visualizer] Inicialização concluída');
        }

        function setupAudioAnalyser(audioContextRef, streamRef, isSolo = false) {
            try {
                console.log('[Visualizer] Configurando analisador de áudio:', {
                    isSolo,
                    hasAudioContext: !!audioContextRef,
                    hasStream: !!streamRef,
                    streamActive: streamRef?.active,
                    contextState: audioContextRef?.state,
                    streamTracks: streamRef?.getTracks().length,
                    audioTracks: streamRef?.getAudioTracks().length
                });

                if (!audioContextRef || !streamRef) {
                    console.warn('[Visualizer] AudioContext ou Stream não disponível');
                    return null;
                }

                // Verificar se o stream tem faixas de áudio ativas
                const audioTracks = streamRef.getAudioTracks();
                if (audioTracks.length === 0) {
                    console.error('[Visualizer] Stream não possui faixas de áudio');
                    return null;
                }

                const audioTrack = audioTracks[0];
                console.log('[Visualizer] Faixa de áudio:', {
                    enabled: audioTrack.enabled,
                    muted: audioTrack.muted,
                    readyState: audioTrack.readyState,
                    settings: audioTrack.getSettings()
                });

                const analyser = audioContextRef.createAnalyser();
                analyser.fftSize = 256;
                analyser.smoothingTimeConstant = 0.8;
                analyser.minDecibels = -90;
                analyser.maxDecibels = -10;

                console.log('[Visualizer] Analisador criado:', {
                    fftSize: analyser.fftSize,
                    frequencyBinCount: analyser.frequencyBinCount,
                    smoothingTimeConstant: analyser.smoothingTimeConstant,
                    minDecibels: analyser.minDecibels,
                    maxDecibels: analyser.maxDecibels
                });

                const source = audioContextRef.createMediaStreamSource(streamRef);
                source.connect(analyser);

                // Teste imediato para verificar se está recebendo dados
                setTimeout(() => {
                    const testData = new Uint8Array(analyser.frequencyBinCount);
                    analyser.getByteFrequencyData(testData);
                    const hasData = testData.some(value => value > 0);
                    console.log('[Visualizer] Teste inicial de dados:', {
                        hasData,
                        maxValue: Math.max(...testData),
                        sampleValues: Array.from(testData.slice(0, 10))
                    });

                    if (!hasData) {
                        console.warn('[Visualizer] ATENÇÃO: Não há dados de áudio sendo recebidos. Verifique:');
                        console.warn('1. Permissões do microfone');
                        console.warn('2. Volume do microfone');
                        console.warn('3. Se o microfone não está mudo');
                        console.warn('4. Se outros aplicativos não estão usando o microfone');
                    }
                }, 1000);

                console.log('[Visualizer] Fonte de áudio conectada ao analisador');

                if (isSolo) {
                    soloAnalyserNode = analyser;
                    console.log('[Visualizer] Analisador solo configurado');
                } else {
                    analyserNode = analyser;
                    console.log('[Visualizer] Analisador multiplayer configurado');
                }

                return analyser;
            } catch (error) {
                console.error('[Visualizer] Erro ao configurar analisador:', error);
                return null;
            }
        }

        function startAudioVisualization(isSolo = false) {
            const analyser = isSolo ? soloAnalyserNode : analyserNode;
            const context = isSolo ? soloVisualizerContext : visualizerContext;
            const canvas = isSolo ? soloAudioVisualizer : audioVisualizer;

            console.log('[Visualizer] Iniciando visualização:', {
                isSolo,
                hasAnalyser: !!analyser,
                hasContext: !!context,
                hasCanvas: !!canvas,
                canvasSize: canvas ? `${canvas.width}x${canvas.height}` : 'N/A'
            });

            if (!analyser || !context || !canvas) {
                console.warn('[Visualizer] Componentes não disponíveis para visualização:', {
                    analyser: !!analyser,
                    context: !!context,
                    canvas: !!canvas
                });
                return;
            }

            const bufferLength = analyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);

            console.log('[Visualizer] Configuração da visualização:', {
                bufferLength,
                dataArrayLength: dataArray.length
            });

            // Marcar como ativo
            if (isSolo) {
                isSoloVisualizerActive = true;
            } else {
                isVisualizerActive = true;
            }

            console.log('[Visualizer] Estado ativo definido:', {
                isSolo,
                isActive: isSolo ? isSoloVisualizerActive : isVisualizerActive
            });

            function draw() {
                if (isSolo ? !isSoloVisualizerActive : !isVisualizerActive) {
                    return;
                }

                analyser.getByteFrequencyData(dataArray);

                // Limpar canvas
                const canvasWidth = canvas.width / window.devicePixelRatio;
                const canvasHeight = canvas.height / window.devicePixelRatio;
                context.clearRect(0, 0, canvasWidth, canvasHeight);

                // Configurar estilo para barras de frequência
                const barWidth = canvasWidth / bufferLength * 2.5;
                let barHeight;
                let x = 0;

                // Desenhar barras de frequência
                for (let i = 0; i < bufferLength; i++) {
                    barHeight = (dataArray[i] / 255) * (canvasHeight - 20);

                    // Só desenhar se houver dados significativos
                    if (dataArray[i] > 0) {
                        // Gradiente de cor baseado na frequência
                        const hue = (i / bufferLength) * 360;
                        const saturation = 70;
                        const lightness = 50 + (dataArray[i] / 255) * 30;

                        context.fillStyle = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
                        context.fillRect(x, canvasHeight - barHeight - 10, barWidth, barHeight);
                    }

                    x += barWidth + 1;
                }

                // Continuar animação
                const animationId = requestAnimationFrame(draw);
                if (isSolo) {
                    soloVisualizerAnimationId = animationId;
                } else {
                    visualizerAnimationId = animationId;
                }
            }

            console.log('[Visualizer] Iniciando loop de desenho');
            draw();
        }

        function stopAudioVisualization(isSolo = false) {
            if (isSolo) {
                isSoloVisualizerActive = false;
                if (soloVisualizerAnimationId) {
                    cancelAnimationFrame(soloVisualizerAnimationId);
                    soloVisualizerAnimationId = null;
                }
                if (soloVisualizerContext && soloAudioVisualizer) {
                    soloVisualizerContext.clearRect(0, 0, soloAudioVisualizer.width / window.devicePixelRatio, soloAudioVisualizer.height / window.devicePixelRatio);
                }
            } else {
                isVisualizerActive = false;
                if (visualizerAnimationId) {
                    cancelAnimationFrame(visualizerAnimationId);
                    visualizerAnimationId = null;
                }
                if (visualizerContext && audioVisualizer) {
                    visualizerContext.clearRect(0, 0, audioVisualizer.width / window.devicePixelRatio, audioVisualizer.height / window.devicePixelRatio);
                }
            }
        }

        // <<< ADICIONAR FUNÇÃO SENDMESSAGE GLOBAL >>>
        function sendMessage(message) {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                // O backend espera que a mensagem completa seja um objeto com type e data
                // Se a 'message' já tem 'type', e o conteúdo principal está em 'data',
                // ou se a 'message' é o próprio conteúdo para 'data' e o 'type' é fixo aqui,
                // isso precisa ser consistente.
                // A implementação atual em app.py para audio_submission espera:
                // { type: "audio_submission", data: { audio_data: "...", song_id_for_audio: "..." } }
                // E para select_song: { type: "select_song", song_id: "...", duet_mode: true/false }
                // A função selectSongForDuel já faz: sendMessage({ type: "select_song", song_id: songId, duet_mode: duetModeEnabled });
                // Então, a estrutura de `message` passada para sendMessage deve estar correta.
                websocket.send(JSON.stringify(message));
            } else {
                console.warn("[SendMessage] WebSocket não está aberto. Mensagem não enviada:", message);
            }
        }

        function showSection(sectionName) {
            Object.values(sections).forEach(sec => sec.classList.remove('active'));
            if (sections[sectionName]) sections[sectionName].classList.add('active');
        }

        function updateUIBasedOnState() {
            console.log("[UI Update] Updating UI for state:", currentLobbyState);
            try {
                if (!currentLobbyState || typeof currentLobbyState !== 'object') {
                    console.warn("[UI Update] Invalid currentLobbyState, showing initial setup.");
                    showSection('initialSetup');
                    if (playerName) playerNameInput.value = playerName;
                    return;
                }

                isHost = currentLobbyState.host_client_id === clientId;
                // const duetCheckboxContainer = document.querySelector('#song-selection-area .form-check'); // Removido pois o checkbox mudou de lugar
                // O checkbox duetModeCheckbox agora é global e seu estado é controlado mais abaixo.

                // Ocultar ou mostrar o checkbox de modo dueto baseado no estado e se é host
                // Essa lógica está correta para a nova posição do checkbox
                // duetModeCheckbox.disabled = !(isHost && currentLobbyState.game_state === 'song_selection'); // Linha original comentada
                // <<< MODIFICADO: Adicionar verificação de número de jogadores para habilitar checkbox de dueto >>>
                const canHostSelectMusic = isHost &&
                                         currentLobbyState.game_state === 'song_selection' &&
                                         Object.keys(currentLobbyState.players || {}).length >= 2;
                duetModeCheckbox.disabled = !canHostSelectMusic;

                // Atualizar o estado do checkbox com base no currentLobbyState.duet_mode apenas se não for o host OU se o jogo não estiver em seleção
                // Para evitar que o host perca sua seleção ao receber um lobby_update.
                if (!isHost || currentLobbyState.game_state !== 'song_selection') {
                    duetModeCheckbox.checked = currentLobbyState.duet_mode === true;
                }

                lobbyId = currentLobbyState.lobby_id || lobbyId;
                lobbyIdDisplay.textContent = lobbyId || 'N/A';
                if(lobbyId) lobbyIdDisplay.title = "Clique para copiar: " + lobbyId;
                else lobbyIdDisplay.title = "ID do Lobby indisponível";

                const playersMap = currentLobbyState.players || {};
                const p1 = playersMap[clientId]; // Jogador local
                let p2 = null; // Oponente
                for (const idInMap in playersMap) {
                    if (idInMap !== clientId) {
                        p2 = playersMap[idInMap];
                        break;
                    }
                }

                // Atualiza informações do Jogador 1 (local)
                if (p1) {
                    player1NameDisplay.innerHTML = `<i class="fas fa-user-check me-2"></i>${p1.name} (Você)`;
                    player1Status.textContent = (websocket && websocket.readyState === WebSocket.OPEN) ? 'Conectado' : 'Desconectado';
                    player1DisplayCard.classList.add('border-success');

                    // LÓGICA DO INDICADOR DE PROGRESSO REFEITA
                    // Mostrar o indicador se p1.is_processing_audio for true.
                    // A porcentagem e a mensagem serão atualizadas pela mensagem 'processing_progress'.
                    // Se o estado for 'results', o indicador é sempre escondido.
                    if (p1.is_processing_audio && currentLobbyState.game_state !== 'results') {
                        if (audioLoadingIndicator.style.display === 'none') {
                             console.log(`[UI Update - Cliente ${clientId}] Mostrando audioLoadingIndicator pois p1.is_processing_audio = true.`);
                             // Define um estado inicial caso a mensagem de progresso ainda não tenha chegado
                             uploadStatusMessage.textContent = "Iniciando processamento...";
                             uploadProgressPercentage.textContent = "0%";
                             audioLoadingIndicator.style.display = 'block';
                             recordBtn.style.display = 'none';
                             stopBtn.style.display = 'none';
                        } // Se já estiver visível, as mensagens de progresso cuidarão de atualizar.
                    } else {
                        // Esconder se não estiver processando OU se estiver na tela de resultados
                        if (audioLoadingIndicator.style.display !== 'none') {
                            console.log(`[UI Update - Cliente ${clientId}] Escondendo audioLoadingIndicator. p1.is_processing_audio = ${p1.is_processing_audio}, game_state = ${currentLobbyState.game_state}`);
                            audioLoadingIndicator.style.display = 'none';
                            // A lógica de mostrar/esconder recordBtn/stopBtn será tratada pelo switch(gameState) abaixo
                        }
                    }
                } else {
                     player1NameDisplay.innerHTML = `<i class="fas fa-user me-2"></i>${playerName || 'Jogador 1'} (Você)`;
                     player1Status.textContent = (websocket && websocket.readyState === WebSocket.OPEN) ? 'Conectado' : 'Desconectado';
                     player1DisplayCard.classList.remove('border-success');
                     if (audioLoadingIndicator.style.display !== 'none') {
                        audioLoadingIndicator.style.display = 'none'; // Garante que está escondido se p1 não existe
                     }
                }

                if (p2) {
                    player2NameDisplay.innerHTML = `<i class="fas fa-user me-2"></i>${p2.name}`;
                    // Assumindo que a informação 'joined' não vem mais diretamente, mas a presença do p2 indica que ele está no lobby.
                    // O estado 'websocket' no backend (excluído do dump) indicaria conexão ativa.
                    // Para simplificar, se p2 existe, consideramos conectado para a UI.
                    player2Status.textContent = 'Conectado';
                    player2DisplayCard.classList.add('border-primary');
                } else {
                    player2NameDisplay.innerHTML = `<i class="fas fa-user-slash me-2"></i>Aguardando Oponente...`;
                    player2Status.textContent = '';
                    player2DisplayCard.classList.remove('border-primary');
                }

                player1DisplayCard.classList.remove('active-turn');
                player2DisplayCard.classList.remove('active-turn');
                if(currentLobbyState.turn_client_id === p1?.client_id) player1DisplayCard.classList.add('active-turn');
                if(currentLobbyState.turn_client_id === p2?.client_id) player2DisplayCard.classList.add('active-turn');

                // <<< AJUSTE PARA currentTurnPlayer e turnPlayerName >>>
                const isMyTurn = currentLobbyState.turn_client_id === clientId;
                const currentTurnPlayer = currentLobbyState.turn_client_id ? playersMap[currentLobbyState.turn_client_id] : null;
                const turnPlayerName = currentTurnPlayer ? currentTurnPlayer.name : 'Ninguém';

                const gameState = currentLobbyState.game_state || 'waiting_for_players';
                const songDetails = currentLobbyState.song_details;
                const currentSongId = currentLobbyState.song_id;
                const isDuetModeActive = currentLobbyState.duet_mode === true;

                switch (gameState) {
                    case 'waiting_for_players':
                        showSection('game');
                        lobbyStatusMessage.textContent = 'Aguardando oponente entrar no lobby...';
                        lobbyStatusMessage.className = 'alert alert-info text-center';
                        songSelectionArea.style.display = 'none';
                        gamePlayArea.style.display = 'none';
                        resultsArea.style.display = 'none';
                        audioPlayer.pause();
                        break;
                    case 'song_selection':
                        showSection('game');
                        // <<< AJUSTE PARA NOME DO HOST EM lobbyStatusMessage >>>
                        const hostNameForSelection = currentLobbyState.host_client_id ? (playersMap[currentLobbyState.host_client_id]?.name || 'Host') : 'Host';
                        lobbyStatusMessage.textContent = isHost ? `${playerName}, selecione uma música para o duelo!` : `Aguardando ${hostNameForSelection} selecionar música.`;
                        lobbyStatusMessage.className = 'alert alert-primary text-center';
                        gamePlayArea.style.display = 'none';
                        resultsArea.style.display = 'none';
                        songSelectionArea.style.display = 'block';
                        // if (isHost) { // <<< MODIFICADO: Condição explícita para o host --- Linha original comentada
                        // <<< MODIFICADO: Usar canHostSelectMusic para controlar UI de seleção de música >>>
                        if (canHostSelectMusic) {
                            // Se este cliente é o host E HÁ JOGADORES SUFICIENTES, popular a lista de músicas se estiver vazia
                            if (songListDiv.children.length === 0 || songListDiv.querySelector('p.text-center')) {
                                populateSongList();
                            }
                            // Garantir que os itens da lista sejam clicáveis para o host
                            Array.from(songListDiv.children).forEach(item => {
                                if (item.classList.contains('list-group-item')) {
                                    item.classList.remove('disabled');
                                    item.style.pointerEvents = 'auto';
                                }
                            });
                        } else if (isHost && currentLobbyState.game_state === 'song_selection') {
                            // Se é o host, estado é song_selection, MAS NÃO HÁ JOGADORES SUFICIENTES
                            songListDiv.innerHTML = '<p class="text-center p-3" style="color: var(--discord-text);">Aguardando oponente para selecionar música...</p>';
                        } else {
                             // Se este cliente NÃO é o host (ou não é song_selection), mostrar mensagem e desabilitar interação com a lista
                             songListDiv.innerHTML = '<p class="text-center p-3" style="color: var(--discord-text);">Aguardando host selecionar música...</p>';
                        }
                        audioPlayer.pause();
                        break;
                    case 'player1_turn':
                    case 'player2_turn':
                        if (isDuetModeActive) {
                            console.warn("[UI Update] Estado incorreto: player_turn em modo dueto. Deveria ser duet_performance.");
                        }
                        showSection('game');
                        lobbyStatusMessage.textContent = `É a vez de ${turnPlayerName} cantar!`;
                        lobbyStatusMessage.className = 'alert alert-warning text-center';
                        songSelectionArea.style.display = 'none';
                        gamePlayArea.style.display = 'block';
                        resultsArea.style.display = 'none';

                        // <<< Verificação MAIS RIGOROSA de songDetails >>>
                        if (songDetails && currentSongId) {
                            // Carrega música e LRC se mudou
                            // Compara audioPlayer.dataset.currentSongId para evitar recarregar se a URL for a mesma mas o objeto mudou
                            if (audioPlayer.dataset.currentSongId !== currentSongId) {
                                loadAndPrepareSong(songDetails, currentSongId);
                            }

                            currentSongTitle.textContent = songDetails?.titulo || 'Música Desconhecida';
                            currentSongArtist.textContent = songDetails?.artista || '';
                            // lyricsDisplay é populado por loadAndPrepareSong
                        } else {
                            // Estado inesperado: turno de jogador mas sem detalhes da música?
                            console.error("[UI Update] Erro: Turno de jogador ativo, mas songDetails está faltando no estado:", currentLobbyState);
                            currentSongTitle.textContent = 'Erro';
                            currentSongArtist.textContent = 'Música não carregada';
                            lyricsDisplay.innerHTML = '<p class="text-danger">Erro ao carregar detalhes da música.</p>';
                        }

                        turnIndicator.textContent = `Vez de: ${turnPlayerName}`;
                        recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar'; // Garante o texto
                        recordBtn.disabled = !isMyTurn || !songDetails;
                        stopBtn.style.display = 'none';
                        recordBtn.style.display = 'inline-block';
                        audioLoadingIndicator.style.display = 'none';

                        // NÃO toca o áudio automaticamente aqui. O áudio só começa ao clicar em "Começar"
                        audioPlayer.pause(); // Garante que está pausado
                        audioPlayer.currentTime = 0; // Reinicia a música para o início
                        break;
                    case 'duet_performance':
                        showSection('game');
                        lobbyStatusMessage.textContent = 'Preparem-se para o Dueto Real!';
                        lobbyStatusMessage.className = 'alert alert-info text-center';
                        songSelectionArea.style.display = 'none';
                        if (gamePlayArea.style.display !== 'block') { // Evitar manipulação desnecessária do DOM
                            gamePlayArea.style.display = 'block';
                        }
                        resultsArea.style.display = 'none';

                        console.log(`[UI Update - Duet Perf] Cliente ${clientId}: Verificando necessidade de recarregar música. Player song ID: ${audioPlayer.dataset.currentSongId}, State song ID: ${currentSongId}`);
                        if (songDetails && currentSongId) {
                            if (audioPlayer.dataset.currentSongId !== currentSongId) {
                                console.log(`[UI Update - Duet Perf] Cliente ${clientId}: IDs de música diferentes. Recarregando música.`);
                                loadAndPrepareSong(songDetails, currentSongId);
                            }
                            currentSongTitle.textContent = songDetails?.titulo || 'Música Desconhecida';
                            currentSongArtist.textContent = songDetails?.artista || '';
                        } else {
                            console.error("[UI Update] Erro: Estado de dueto ativo, mas songDetails está faltando no estado:", currentLobbyState);
                            currentSongTitle.textContent = 'Erro';
                            currentSongArtist.textContent = 'Música não carregada';
                            lyricsDisplay.innerHTML = '<p class="text-danger">Erro ao carregar detalhes da música.</p>';
                        }

                        // IMPORTANTE: Verificar se estou ativamente gravando antes de alterar botões/controles
                        const amIActivelyRecording = myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording';
                        console.log(`[UI Update - Duet Perf - Cliente ${clientId}] Verificação de gravação ativa: ${amIActivelyRecording}`);

                        turnIndicator.textContent = `Dueto: Todos cantam juntos!`;

                        // Se estou gravando ativamente, mantenho a UI em estado de gravação
                        // independente do que o servidor diz
                        if (amIActivelyRecording) {
                            console.log(`[UI Update - Duet Perf - Cliente ${clientId}] PROTEÇÃO DO DUETO: Estou gravando, mantendo botões e controles no estado 'gravando'.`);

                            // NUNCA interromper a música se estou em estado de gravação ativa!
                            // Isso evita que a atualização do estado vinda do servidor interrompa minha gravação

                            // Proteger contra pausa automática do áudio (verificar se está tocando)
                            if (audioPlayer.paused && audioPlayer.currentTime > 0 && audioPlayer.currentTime < audioPlayer.duration) {
                                console.log(`[UI Update - Duet Perf - Cliente ${clientId}] RESTAURANDO reprodução de áudio que foi pausada!`);
                                try {
                                    audioPlayer.play();
                                } catch (e) {
                                    console.error(`[UI Update - Duet Perf - Cliente ${clientId}] Erro ao tentar restaurar o áudio:`, e);
                                }
                            }

                            recordBtn.style.display = 'none';
                            stopBtn.style.display = 'inline-block';
                            // Garantir que o texto do botão 'stop' esteja correto
                            stopBtn.innerHTML = '<span class="recording-indicator"></span><i class="fas fa-stop-circle me-2"></i>Parar Gravação (Dueto)';
                            recordBtn.disabled = true;
                            audioLoadingIndicator.style.display = 'none';
                        } else {
                            // --- Lógica Refinada para botões gravação/parada ---
                            console.log(`[UI Update - Duet Perf - Cliente ${clientId}] Verificando estado atual do MediaRecorder: ${myActiveDuetMediaRecorder?.state}`);

                            // Se o duet_performance já foi iniciado e este jogador está gravando
                            if (myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording') {
                                // Se este cliente está gravando, mantém os botões no estado correto
                                console.log(`[UI Update - Duet Perf - Cliente ${clientId}] Mantendo botões no estado 'gravando'.`);
                                recordBtn.style.display = 'none';
                                stopBtn.style.display = 'inline-block';
                                // Garante que o texto do botão 'stop' esteja correto (pode ter sido alterado)
                                stopBtn.innerHTML = '<span class="recording-indicator"></span><i class="fas fa-stop-circle me-2"></i>Parar Gravação (Dueto)';
                                recordBtn.disabled = true; // Garante que o botão 'começar' esteja desabilitado
                                audioLoadingIndicator.style.display = 'none';
                            } else {
                                // Se não está gravando (antes de começar, ou depois de parar/erro)
                                // Configura/reseta para o estado inicial do dueto
                                 console.log(`[UI Update - Duet Perf - Cliente ${clientId}] Configurando/resetando botões para 'Começar Dueto' (Estado atual do gravador: ${myActiveDuetMediaRecorder?.state})`);
                                 recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                                 // Habilita botão apenas se não houver gravador ativo ou se ele estiver inativo
                                 recordBtn.disabled = !(myActiveDuetMediaRecorder === null || myActiveDuetMediaRecorder.state === 'inactive');
                                 stopBtn.style.display = 'none';
                                 recordBtn.style.display = 'inline-block';
                                 // Esconde o loading (pode ser mostrado pelo onstop se estiver processando)
                                 // Se o estado for inativo (acabou de parar), o onstop pode mostrar o loading
                                 if (myActiveDuetMediaRecorder?.state !== 'inactive') {
                                     audioLoadingIndicator.style.display = 'none';
                                 }
                            }
                            // --- Fim da Lógica Refinada ---
                        }
                        break;
                    case 'results':
                        showSection('game');
                        lobbyStatusMessage.textContent = 'Duelo finalizado!';
                        lobbyStatusMessage.className = 'alert alert-success text-center';
                        songSelectionArea.style.display = 'none';
                        gamePlayArea.style.display = 'none';
                        resultsArea.style.display = 'block';
                        // Garante que indicador some ao chegar nos resultados
                        // audioLoadingIndicator.style.display = 'none';
                        // Pausa o áudio ao mostrar resultados
                        audioPlayer.pause();
                        displayResults();
                        break;
                    case 'opponent_left':
                        showSection('game');
                        lobbyStatusMessage.textContent = 'Seu oponente saiu do lobby. Você pode sair ou aguardar.';
                        lobbyStatusMessage.className = 'alert alert-danger text-center';
                        songSelectionArea.style.display = 'none';
                        gamePlayArea.style.display = 'none';
                        resultsArea.style.display = 'none';
                        break;
                    default:
                        // Se cair aqui, algo está errado com o estado recebido
                        console.warn("[UI Update] Estado de jogo não reconhecido:", gameState, "Estado completo:", currentLobbyState);
                        // Tenta mostrar a seção do jogo mesmo assim, mas com uma mensagem de erro
                        showSection('game');
                        lobbyStatusMessage.textContent = 'Erro: Estado do jogo inválido recebido do servidor.';
                        lobbyStatusMessage.className = 'alert alert-danger text-center';
                        songSelectionArea.style.display = 'none';
                        gamePlayArea.style.display = 'none';
                        resultsArea.style.display = 'none';
                }
            } catch (error) {
                console.error("[UI Update] Erro ao atualizar UI:", error);
                console.error("[UI Update] Estado atual que causou erro:", currentLobbyState);
                alert("Ocorreu um erro ao atualizar a interface. Verifique o console.");
            }
        }

        async function populateSongList() {
            // Busca as músicas se songsData estiver vazio
            if (Object.keys(songsData).length === 0) {
                try {
                    const response = await fetch('/api/songs');
                    if (!response.ok) throw new Error('Falha ao buscar músicas');
                    songsData = await response.json();
                } catch (error) {
                    console.error("Erro ao buscar músicas:", error);
                    songListDiv.innerHTML = '<li class="apple-style-list-item text-danger">Erro ao carregar lista de músicas.</li>';
                    return;
                }
            }

            songListDiv.innerHTML = ''; // Limpa lista antiga
            if (Object.keys(songsData).length === 0) {
                songListDiv.innerHTML = '<li class="apple-style-list-item">Nenhuma música disponível no servidor.</li>';
                return;
            }

            for (const songId in songsData) {
                const song = songsData[songId];
                const listItem = document.createElement('li');
                listItem.className = 'apple-style-list-item';
                listItem.dataset.songId = songId;
                listItem.innerHTML = `
                    <div class="apple-style-song-info">
                        <span class="apple-style-song-title">${song.titulo}</span>
                        <span class="apple-style-song-artist">${song.artista}</span>
                    </div>
                    <span class="material-icons-outlined apple-style-play-icon">play_circle_filled</span>
                `;
                listItem.onclick = () => {
                    if (currentLobbyState.host_client_id === clientId && currentLobbyState.game_state === 'song_selection') {
                        // Adicionar classe 'active' ao item clicado (opcional, para feedback visual)
                        document.querySelectorAll('#songList .apple-style-list-item').forEach(item => item.classList.remove('active'));
                        listItem.classList.add('active');
                        selectSongForDuel(songId);
                    } else {
                        console.warn("Tentativa de selecionar música por não-host ou em estado incorreto.");
                    }
                };
                songListDiv.appendChild(listItem);
            }
        }

        // Função para selecionar uma música para o duelo (HOST)
        function selectSongForDuel(songId) {
            if (!isHost) {
                console.warn("Apenas o host pode selecionar a música.");
                return;
            }
            const duetModeEnabled = document.getElementById('duetModeCheckbox').checked; // <<< LER CHECKBOX
            console.log(`Host selecionou a música: ${songId}, Modo Dueto: ${duetModeEnabled}`);
            sendMessage({ type: "select_song", song_id: songId, duet_mode: duetModeEnabled }); // <<< ENVIAR FLAG
        }

        function displayResults() {
            // <<< AJUSTE PARA LER JOGADORES DO playersMap EM displayResults >>>
            const playersMapForResult = currentLobbyState.players || {};
            let player1Result = null;
            let player2Result = null;
            let playerIds = Object.keys(playersMapForResult);

            if (playerIds.length > 0) {
                // Tenta encontrar o jogador local (p1) e o outro jogador (p2)
                const localPlayerId = clientId;
                player1Result = playersMapForResult[localPlayerId];

                const opponentId = playerIds.find(id => id !== localPlayerId);
                if (opponentId) {
                    player2Result = playersMapForResult[opponentId];
                }
            }

            // Se não encontrou p1 pelo clientId (talvez em um cenário de observador, ou se clientId mudou)
            // ou se só tem 1 jogador, tenta pegar o primeiro da lista como p1.
            if (!player1Result && playerIds.length > 0) {
                player1Result = playersMapForResult[playerIds[0]];
                if (playerIds.length > 1 && playerIds[0] === clientId) { // Se p1 era o local, pega o próximo como p2
                     player2Result = playersMapForResult[playerIds[1]];
                } else if (playerIds.length > 1) { // Se p1 não era o local, o segundo é p2
                     player2Result = playersMapForResult[playerIds[1]];
                }
            } else if (player1Result && !player2Result && playerIds.length > 1) {
                // Se p1 foi encontrado e p2 não (e há mais de 1 jogador), p2 deve ser o outro.
                const opponentId = playerIds.find(id => id !== player1Result.client_id);
                if (opponentId) player2Result = playersMapForResult[opponentId];
            }

            resultPlayer1Name.textContent = player1Result?.name || 'Jogador 1';
            // <<< Ajustar exibição de score para mostrar erro >>>
            if (player1Result?.processing_error) {
                resultPlayer1Score.textContent = 'Erro';
                resultPlayer1Score.style.color = 'var(--discord-red)'; // Cor vermelha para erro
            } else {
                resultPlayer1Score.textContent = `${player1Result?.score || 0}%`;
                resultPlayer1Score.style.color = 'var(--discord-green)'; // Cor verde padrão
            }

            resultPlayer2Name.textContent = player2Result?.name || (playerIds.length < 2 && player1Result ? '-' : 'Jogador 2');
             // <<< Ajustar exibição de score para mostrar erro >>>
            if (player2Result?.processing_error) {
                resultPlayer2Score.textContent = 'Erro';
                resultPlayer2Score.style.color = 'var(--discord-red)'; // Cor vermelha para erro
            } else {
                resultPlayer2Score.textContent = `${player2Result?.score || 0}%`;
                resultPlayer2Score.style.color = 'var(--discord-green)'; // Cor verde padrão
            }

            // <<< Lógica do Vencedor precisa considerar erros >>>
            if (player1Result && player2Result) {
                if (player1Result.processing_error && player2Result.processing_error) {
                    duelWinner.textContent = "Erro para ambos os jogadores!";
                } else if (player1Result.processing_error) {
                    duelWinner.textContent = `${player2Result.name} Venceu (Oponente teve erro)!`;
                } else if (player2Result.processing_error) {
                     duelWinner.textContent = `${player1Result.name} Venceu (Oponente teve erro)!`;
                } else if (player1Result.score > player2Result.score) {
                    duelWinner.textContent = `${player1Result.name} Venceu!`;
                } else if (player2Result.score > player1Result.score) {
                    duelWinner.textContent = `${player2Result.name} Venceu!`;
                } else {
                    duelWinner.textContent = "Empate Incrível!";
                }
            } else if (player1Result && !player2Result && playerIds.length === 1) {
                // Apenas um jogador, mostra o resultado dele
                 duelWinner.textContent = `Resultado de ${player1Result.name}`;
                 if (player1Result.processing_error) { // Adiciona info se teve erro
                      duelWinner.textContent += " (Erro no processamento)";
                 }
            } else {
                 duelWinner.textContent = "Resultado Indisponível" + (Object.keys(playersMapForResult).length < 2 ? " (Oponente saiu?)" : "");
            }

            const originalLyricsText = currentLobbyState.song_lyrics || currentLobbyState.song_details?.letra || "Letra original não disponível.";
            resultOriginalLyrics.innerHTML = ''; // Limpa
            originalLyricsText.split('\n').forEach(line => {
                const p = document.createElement('p');
                p.textContent = line;
                p.style.margin = '0 0 5px 0'; // Pequena margem inferior
                resultOriginalLyrics.appendChild(p);
            });
        }

        function connectWebSocket() {
            if (!lobbyId || !clientId) {
                console.error("Lobby ID ou Client ID não definidos para WebSocket.");
                return;
            }
            const wsFullUrl = `${WS_URL}/${lobbyId}/${clientId}`;
            websocket = new WebSocket(wsFullUrl);

            websocket.onopen = () => {
                console.log(`[WebSocket] Conectado para lobby ${lobbyId}, cliente ${clientId}`);
                // Atualiza UI mínima indicando conexão, antes mesmo da primeira mensagem
                lobbyIdDisplay.textContent = lobbyId;
                lobbyIdDisplay.title = "Clique para copiar: " + lobbyId;
                player1Status.textContent = 'Conectado ao Lobby';
                // A atualização completa virá com o lobby_update
            };

            websocket.onmessage = (event) => {
                console.log("[WebSocket] Raw message received:", event.data);
                try {
                    const message = JSON.parse(event.data);
                    console.log("[WebSocket] Parsed message:", message);

                    if (message.type === 'lobby_update') {
                        if (message.data && typeof message.data === 'object') {
                            // Log Adicional para verificar o estado do audioPlayer ANTES de atualizar a UI
                            // e os dados recebidos no lobby_update.
                            console.log(`[Lobby Update ANTES de UI Update - Cliente ${clientId}] Estado do audioPlayer: Paused=${audioPlayer.paused}, Ended=${audioPlayer.ended}, CurrentTime=${audioPlayer.currentTime}, ReadyState=${audioPlayer.readyState}`);
                            console.log(`[Lobby Update ANTES de UI Update - Cliente ${clientId}] Dados recebidos:`, JSON.stringify(message.data));

                            // IMPORTANTE: Verificar se estamos em modo dueto e gravando ativamente
                            // Armazenar o estado do MediaRecorder antes de atualizar qualquer coisa
                            const isInDuetMode = currentLobbyState?.duet_mode === true;
                            const isCurrentlyRecording = myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording';
                            // const previousIsProcessingAudio = currentLobbyState?.players?.[clientId]?.is_processing_audio || false; // Removido, não usado mais diretamente aqui

                            if (isInDuetMode && isCurrentlyRecording) {
                                console.log(`[Proteção do Dueto - Cliente ${clientId}] ANTES de atualizar estado: Estou ativamente gravando em modo dueto.`);
                            }

                            // Atualiza o estado do lobby com os dados recebidos
                            currentLobbyState = message.data;
                            updateUIBasedOnState();

                            // Proteção para dueto (manter UI de gravação se estiver gravando)
                            // Esta lógica parece correta e deve ser mantida após updateUIBasedOnState
                            if (isInDuetMode && isCurrentlyRecording) {
                                const amIStillMarkedAsRecording = myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording';
                                const isP1ProcessingAudioNow = currentLobbyState?.players?.[clientId]?.is_processing_audio || false;

                                console.log(`[Dueto Proteção Pós-UI Update - Cliente ${clientId}] Verificando. StillMarkedRecording: ${amIStillMarkedAsRecording}, P1Processing: ${isP1ProcessingAudioNow}`);

                                if (amIStillMarkedAsRecording && !isP1ProcessingAudioNow) {
                                    console.log(`[Dueto Proteção Pós-UI Update - Cliente ${clientId}] Ainda estou gravando, mas o estado do servidor não reflete 'is_processing_audio'. Forçando UI de gravação.`);
                                    stopBtn.style.display = 'inline-block';
                                    recordBtn.style.display = 'none';
                                    stopBtn.innerHTML = '<span class="recording-indicator"></span><i class="fas fa-stop-circle me-2"></i>Continue Cantando! (Parar)';
                                    audioLoadingIndicator.style.display = 'none'; // Esconde o loading se estivermos forçando a UI de gravação
                                }
                            }

                        } else {
                            console.error("[WebSocket] Estrutura inválida de 'lobby_update':", message);
                        }
                    } else if (message.type === 'begin_duet_now') {
                        console.log(`[Dueto] Recebido 'begin_duet_now' como cliente ${clientId}.`);
                        if (currentLobbyState.game_state === 'duet_performance') {
                            console.log(`[Dueto] Início sincronizado! Chamando playAndRecordForDuet().`);
                            playAndRecordForDuet();
                        } else {
                            console.warn("[Dueto] Recebido 'begin_duet_now' mas não está em 'duet_performance'. Estado atual:", currentLobbyState.game_state);
                        }
                    // ADICIONADO: Tratar mensagens de progresso de processamento
                    } else if (message.type === 'processing_progress') {
                        // Verifica se a mensagem de progresso é para este cliente (o backend agora envia direto)
                        // Não precisamos mais verificar message.client_id === clientId se o backend envia direto.
                        console.log("[WebSocket] Recebido processing_progress:", message);
                        if (uploadStatusMessage && uploadProgressPercentage) {
                            uploadStatusMessage.textContent = message.status_message || "Processando...";
                            uploadProgressPercentage.textContent = (message.progress || 0) + "%";
                            audioLoadingIndicator.style.display = 'block'; // Garante que está visível

                            // Esconder botões de gravação enquanto o progresso é exibido
                            recordBtn.style.display = 'none';
                            stopBtn.style.display = 'none';

                            // Se for uma mensagem de erro vinda do progresso, aplicar estilo de erro
                            if (message.error) {
                                uploadStatusMessage.style.color = 'var(--discord-red)';
                                uploadProgressPercentage.style.color = 'var(--discord-red)';
                            } else {
                                uploadStatusMessage.style.color = 'var(--discord-light-gray)'; // Cor padrão
                                uploadProgressPercentage.style.color = 'var(--discord-white)';  // Cor padrão
                            }

                            // Se o progresso for 100% e não for erro, podemos opcionalmente esconder após um delay
                            // No entanto, a lógica em updateUIBasedOnState (quando is_processing_audio se torna false)
                            // já deve cuidar de esconder o indicador.
                            // Só precisamos garantir que o 100% seja mostrado.
                        }
                    } else if (message.type === 'error') {
                        console.error("[WebSocket] Server error message:", message.message);
                        alert(`Erro do servidor: ${message.message}`);
                    }
                    // Lidar com outros tipos de mensagens se necessário
                } catch (error) {
                    console.error("[WebSocket] Error processing message:", error);
                    console.error("[WebSocket] Original message data:", event.data);
                    alert("Ocorreu um erro ao processar dados do servidor. Verifique o console.");
                }
            };

            websocket.onerror = (error) => {
                console.error("Erro no WebSocket:", error);
                lobbyStatusMessage.textContent = 'Erro de conexão com o lobby. Tente recarregar.';
                lobbyStatusMessage.className = 'alert alert-danger text-center';
            };

            websocket.onclose = (event) => {
                console.log("WebSocket desconectado:", event.reason, event.code);
                lobbyStatusMessage.textContent = 'Desconectado do lobby.';
                 lobbyStatusMessage.className = 'alert alert-warning text-center';
                // Tentar reconectar ou limpar UI?
                // Por agora, vamos apenas indicar desconexão.
                // Poderia setar game_state para 'opponent_left' ou similar no cliente se apropriado.
            };
        }

        // Lógica de Gravação de Áudio (REFEITA E SIMPLIFICADA)
        recordBtn.addEventListener('click', async () => {
            // <<< PASSO 5: LÓGICA DE INÍCIO SINCRONIZADO (FRONTEND) >>>
            if (currentLobbyState.duet_mode === true && currentLobbyState.game_state === 'duet_performance') {
                console.log("[Record Btn] Modo Dueto detectado. Enviando start_duet_request.");
                sendMessage({ type: "start_duet_request" });
                recordBtn.disabled = true;
                recordBtn.innerHTML = '<i class="fas fa-hourglass-half me-2"></i>Aguardando Sincronia...';
                return;
            }

            // Lógica original para modo de revezamento
            if (!currentLobbyState.song_id || currentLobbyState.turn_client_id !== clientId) {
                alert('Não é sua vez de gravar ou nenhuma música selecionada!');
                return;
            }

            // Parar monitoramento anterior se estiver ativo (garantia)
            stopMonitoring();

            // 1. Configurar gravação do microfone (sem iniciar ainda)
            try {
                console.log('[Mic Setup] Configurando microfone com sampleRate fixo e filtros desabilitados...');
                microphoneStream = await navigator.mediaDevices.getUserMedia({
                    // Aplicando Solução 1 sugerida:
                    audio: {
                        sampleRate: 44100,      // Tentar forçar uma taxa padrão
                        channelCount: 1,        // Gravar em mono
                        echoCancellation: false,// Desabilitar filtros automáticos
                        noiseSuppression: false,
                        autoGainControl: false
                    }
                    // Original: audio: { echoCancellation: true, noiseSuppression: true, autoGainControl: true }
                });
                console.log('[Mic Setup] Stream do microfone obtido.');

                mediaRecorder = new MediaRecorder(microphoneStream);
                console.log(`[Mic Setup] MediaRecorder pronto. MimeType (padrão do navegador): ${mediaRecorder.mimeType}`);
                audioChunks = []; // Limpar chunks antes de uma nova gravação

                mediaRecorder.addEventListener('dataavailable', event => {
                    if (event.data.size > 0) audioChunks.push(event.data);
                });

                mediaRecorder.addEventListener('stop', () => {
                    const stopClientId = clientId; // Capture client ID
                    // <<< LOG ADICIONAL >>>
                    console.warn(`!!!!! [GLOBAL MediaRecorder ONSTOP - Cliente ${stopClientId}] Evento 'stop' do mediaRecorder GLOBAL/LEGADO disparado! Estado do Dueto: ${currentLobbyState.duet_mode} !!!!!`);

                    console.log('[Recording] MediaRecorder.onstop.'); // Log original

                    // <<< PARAR VISUALIZADOR >>>
                    stopVisualizerWithRecording(false);

                    // Parar monitoramento quando a gravação para
                    stopMonitoring();
                    monitorBtn.disabled = true; // Desabilitar monitoramento quando não está gravando

                    if (microphoneStream) { // Este é o global, que stopMonitoring já parou
                        // microphoneStream.getTracks().forEach(track => track.stop()); // Redundante, stopMonitoring já faz
                        microphoneStream = null;
                        console.log('[Mic Cleanup] Stream do microfone global parado (via onstop global).'); // Ajustado log
                    }

                    // Parar e resetar o player de música HTML5 apenas no modo revezamento
                    // No modo dueto, NÃO paramos o audioPlayer para não interromper os outros jogadores
                    if (audioPlayer && !currentLobbyState.duet_mode) {
                        audioPlayer.pause();
                        audioPlayer.currentTime = 0;
                        console.log('[Audio Element] Música parada (via onstop) - modo revezamento.');
                    }

                    if (audioChunks.length === 0) {
                        console.warn('[Recording] Nenhum dado de áudio gravado.');
                        audioLoadingIndicator.style.display = 'none';
                        recordBtn.style.display = 'inline-block';
                        stopBtn.style.display = 'none';
                        return;
                    }
                    const audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType || 'audio/webm' });
                    const reader = new FileReader();
                    reader.readAsDataURL(audioBlob);
                    reader.onloadend = () => {
                        const base64Audio = reader.result;
                        if (websocket && websocket.readyState === WebSocket.OPEN) {
                            console.log('[Recording] Preparando para enviar audio_submission...');
                            websocket.send(JSON.stringify({
                                type: "audio_submission",
                                data: { audio_data: base64Audio, song_id_for_audio: currentLobbyState.song_id }
                            }));
                            console.log('[Recording] audio_submission enviado.');

                            // <<< REGISTRAR TIMESTAMP DO ENVIO DE ÁUDIO >>>
                            myAudioSubmissionTimestamp = Date.now();
                            console.log(`[Recording] Timestamp do envio registrado: ${myAudioSubmissionTimestamp}`);

                            // Mostrar indicador de progresso com 0% e mensagem inicial
                            if (uploadStatusMessage && uploadProgressPercentage) {
                                uploadStatusMessage.textContent = "Enviando para o servidor...";
                                uploadProgressPercentage.textContent = "0%";
                                uploadStatusMessage.style.color = 'var(--discord-light-gray)'; // Resetar cor caso tenha sido erro antes
                                uploadProgressPercentage.style.color = 'var(--discord-white)'; // Resetar cor
                            }
                            audioLoadingIndicator.style.display = 'block';
                            recordBtn.style.display = 'none';
                            stopBtn.style.display = 'none';
                        }
                    };
                });
                console.log('[Mic Setup] MediaRecorder configurado.');
                monitorBtn.disabled = false; // <<< Habilitar botão de monitoramento após obter stream

            } catch (error) {
                console.error('[Mic Setup] Falha ao configurar microfone:', error);
                let userMessage = "Não foi possível acessar o microfone. Verifique as permissões.";
                if (error.name === 'NotAllowedError') {
                    userMessage = "Permissão para usar o microfone foi negada. Verifique as configurações do navegador para este site.";
                } else if (error.name === 'NotFoundError') {
                    userMessage = "Nenhum microfone encontrado no dispositivo.";
                } else if (error.name === 'NotReadableError') {
                    userMessage = "Erro de hardware: Não foi possível ler o dispositivo de microfone.";
                } else if (error.name === 'SecurityError') {
                    userMessage = "Erro de segurança ao acessar o microfone. Certifique-se de que a página está carregada via HTTPS ou localhost.";
                } else if (error.name === 'AbortError') {
                    userMessage = "A solicitação do microfone foi abortada, possivelmente por um problema de configuração ou driver.";
                } else {
                    userMessage = `Erro inesperado ao acessar microfone: ${error.name} - ${error.message}`;
                }
                alert(userMessage);
                return;
            }

            // 2. Tocar música com o elemento <audio> padrão do HTML5
            if (audioPlayer && audioPlayer.src) {
                audioPlayer.currentTime = 0; // Reiniciar a música
                console.log('[Audio Element] Tentando tocar música via elemento <audio>...');
                try {
                    await audioPlayer.play(); // .play() retorna uma Promise
                    console.log('[Audio Element] Música iniciada via elemento <audio>.');

                    // 3. Iniciar gravação do microfone AGORA que a música (supostamente) está tocando
                    if (mediaRecorder) {
                        mediaRecorder.start();
                        console.log('[Recording] Gravação do microfone iniciada.');
                        recordBtn.style.display = 'none';
                        stopBtn.style.display = 'inline-block';

                        // <<< INICIAR VISUALIZADOR >>>
                        startVisualizerWithRecording(false);
                    } else {
                        // Isso não deveria acontecer se o setup do microfone foi bem-sucedido
                        console.error('[Recording] MediaRecorder não estava pronto para iniciar.');
                        alert('Erro crítico ao tentar iniciar gravação. Tente recarregar.');
                        if (microphoneStream) {
                            microphoneStream.getTracks().forEach(track => track.stop());
                            microphoneStream = null;
                        }
                        audioPlayer.pause(); // Parar música se a gravação não puder começar
                        return;
                    }
                } catch (error) {
                    console.error('[Audio Element/Recording] Erro ao tocar música ou iniciar gravação:', error);
                    alert('Erro ao iniciar música ou gravação. Verifique o console e tente novamente.');
                    if (microphoneStream) {
                        microphoneStream.getTracks().forEach(track => track.stop());
                        microphoneStream = null;
                    }
                    // Não precisa parar musicSourceNode pois ele não existe mais
                    recordBtn.style.display = 'inline-block'; // Restaurar botão Começar
                    stopBtn.style.display = 'none';
                    return;
                }
            } else {
                alert("Música não está pronta para tocar ou não selecionada.");
                if (microphoneStream) { // Limpar microfone se não há música
                    microphoneStream.getTracks().forEach(track => track.stop());
                    microphoneStream = null;
                }
                return;
            }
        });

        // <<< LÓGICA DE PARAR GRAVAÇÃO E SUBMETER (UNIFICADA) >>>
        function stopRecordingAndSubmit() {
            const whoIsCalling = clientId; // Captura o clientId no momento da chamada
            console.log(`[Stop Recording Logic - Cliente ${whoIsCalling}] Chamado stopRecordingAndSubmit.`);
            console.log(`[Stop Recording Logic - Cliente ${whoIsCalling}] Estado atual do lobby: game_state=${currentLobbyState.game_state}, duet_mode=${currentLobbyState.duet_mode}`);
            console.log(`[Stop Recording Logic - Cliente ${whoIsCalling}] Referência myActiveDuetMediaRecorder: ${myActiveDuetMediaRecorder ? 'existe' : 'NULA'}, estado: ${myActiveDuetMediaRecorder?.state}`);

            // <<< PARAR VISUALIZADOR >>>
            stopVisualizerWithRecording(false);

            if (currentLobbyState.duet_mode === true && currentLobbyState.game_state === 'duet_performance') {
                // Modo Dueto: usa myActiveDuetMediaRecorder
                // if (myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state !== 'inactive') {
                //     console.log('[Stop Recording Logic - Dueto] Parando myActiveDuetMediaRecorder.');
                //     myActiveDuetMediaRecorder.stop(); // Aciona o 'onstop' específico do dueto
                // } else {
                //     console.log('[Stop Recording Logic - Dueto] myActiveDuetMediaRecorder não estava ativo ou já parado.');
                // }
                if (myActiveDuetMediaRecorder) { // Verifica se a referência existe
                    if (myActiveDuetMediaRecorder.state !== 'inactive') {
                        console.log(`[Stop Recording Logic - Dueto - Cliente ${whoIsCalling}] Parando myActiveDuetMediaRecorder (estado ATUAL: ${myActiveDuetMediaRecorder.state}).`);
                        try {
                            myActiveDuetMediaRecorder.stop(); // Aciona o 'onstop' específico do dueto
                        } catch (e) {
                            console.error(`[Stop Recording Logic - Dueto - Cliente ${whoIsCalling}] Erro ao chamar .stop() em myActiveDuetMediaRecorder:`, e);
                        }
                    } else {
                        console.log(`[Stop Recording Logic - Dueto - Cliente ${whoIsCalling}] myActiveDuetMediaRecorder JÁ ESTAVA inativo. Não chamando .stop().`);
                    }
                } else {
                    console.warn(`[Stop Recording Logic - Dueto - Cliente ${whoIsCalling}] Tentativa de parar, mas myActiveDuetMediaRecorder é NULO/UNDEFINED.`);
                }
            } else {
                // Modo Revezamento (ou outro): usa o mediaRecorder global legado
                if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                    console.log(`[Stop Recording Logic - Revezamento/Outro - Cliente ${whoIsCalling}] Parando mediaRecorder (global).`);
                    mediaRecorder.stop(); // Aciona o 'onstop' do mediaRecorder global
                } else {
                    console.log(`[Stop Recording Logic - Revezamento/Outro - Cliente ${whoIsCalling}] mediaRecorder (global) não estava ativo ou já parado.`);
                }
            }
            // A UI do botão stopBtn é geralmente escondida pelo onstop ou por uma atualização de UI do lobby.
            // stopBtn.style.display = 'none';
        }

        stopBtn.addEventListener('click', () => {
            const stopBtnClientId = clientId; // Capturar o ID do cliente que clicou
            console.log(`[UI] Botão Parar clicado pelo cliente ${stopBtnClientId}.`);

            // Verificação adicional para garantir que estamos parando apenas nossa própria gravação
            const isDuetMode = currentLobbyState?.duet_mode === true;
            const amIRecording = myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording';

            if (isDuetMode) {
                console.log(`[UI - Stop Button - Cliente ${stopBtnClientId}] Modo dueto: Verificando estado da gravação antes de parar.`);

                if (amIRecording) {
                    console.log(`[UI - Stop Button - Cliente ${stopBtnClientId}] Eu estou gravando, vou parar minha própria gravação.`);
                    // Chama a função unificada para parar e submeter
                    stopRecordingAndSubmit();
                } else {
                    console.log(`[UI - Stop Button - Cliente ${stopBtnClientId}] Não estou gravando, mas o botão parar foi clicado. Estado inválido.`);
                    alert("Não foi possível parar a gravação - não está ativa. Recarregue a página se o problema persistir.");

                    // Corrigir estado da UI
                    stopBtn.style.display = 'none';
                    if (recordBtn.style.display === 'none') {
                        recordBtn.style.display = 'inline-block';
                        recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                        recordBtn.disabled = false;
                    }
                }
            } else {
                // Em modo não-dueto, usar a lógica original
                stopRecordingAndSubmit();
            }
        });

        // <<< Adicionar Listener para o botão de Monitoramento >>>
        monitorBtn.addEventListener('click', () => {
            if (!isMonitoring) {
                // Só pode iniciar se o stream do microfone já foi obtido (após clicar em Começar)
                if (microphoneStream && microphoneStream.active) {
                    startMonitoring();
                } else {
                    alert('Por favor, clique em "Começar" primeiro para ativar o microfone antes de habilitar o retorno.');
                }
            } else {
                stopMonitoring();
            }
        });

        // Inicialização da UI
        document.addEventListener('DOMContentLoaded', async () => { // Tornar async
            // Verifica suporte básico para MediaDevices
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                console.error("Erro: MediaDevices API não suportada neste navegador.");
                alert("Seu navegador não suporta a gravação de áudio necessária para este aplicativo. Tente usar Chrome ou Firefox atualizados.");
                // Desabilitar botões que dependem do microfone?
                recordBtn.disabled = true;
                recordBtn.title = "Gravação não suportada pelo navegador";
            }

            // Lê o nome salvo, se existir
            playerName = localStorage.getItem('karaokePlayerName') || "";
            playerNameInput.value = playerName;

            // SEMPRE mostra a seção de setup inicial ao carregar a página
            showSection('initialSetup');
            // updateUIBasedOnState(); // Não chama mais aqui, espera confirmação do nome

            // <<< TENTAR CARREGAR DADOS DAS MÚSICAS ANTECIPADAMENTE >>>
            try {
                await ensureSongsDataLoaded();
                console.log("[DOMContentLoaded] Pré-carregamento de songsData bem-sucedido.");
            } catch (e) {
                console.warn("[DOMContentLoaded] Falha ao pré-carregar songsData, tentará novamente mais tarde:", e.message);
            }

            setPlayerNameBtn.addEventListener('click', () => {
                const name = playerNameInput.value.trim();
                if (name) {
                    playerName = name;
                    localStorage.setItem('karaokePlayerName', playerName);
                    // Após confirmar o nome, mostra as opções de lobby
                    showSection('lobbyActions');
                } else {
                    alert('Por favor, insira um nome válido.');
                }
            });

            createLobbyBtn.addEventListener('click', async () => {
                const formData = new FormData();
                formData.append('player_name', playerName);
                try {
                    const response = await fetch('/lobbies/create', { method: 'POST', body: formData });
                    if (!response.ok) throw new Error(`Erro ao criar lobby: ${response.statusText}`);
                    const data = await response.json();
                    lobbyId = data.lobby_id;
                    clientId = data.client_id;
                    localStorage.setItem('myClientIdAtLobbyCreation', clientId); // Para tentar identificar P1 depois
                    showSection('game');
                    connectWebSocket();
                } catch (error) {
                    console.error("Falha ao criar lobby:", error);
                    alert(`Não foi possível criar o lobby: ${error.message}`);
                }
            });

            joinLobbyBtn.addEventListener('click', async () => {
                const idToJoin = lobbyIdInput.value.trim();
                if (!idToJoin) {
                    alert("Por favor, insira um ID de Lobby.");
                    return;
                }
                const formData = new FormData();
                formData.append('player_name', playerName);
                try {
                    const response = await fetch(`/lobbies/${idToJoin}/join`, { method: 'POST', body: formData });
                    if (!response.ok) {
                         const errorData = await response.json().catch(() => ({message: "Erro desconhecido ao entrar no lobby."}));
                         throw new Error(errorData.message || response.statusText);
                    }
                    const data = await response.json();
                    lobbyId = data.lobby_id;
                    clientId = data.client_id;
                    showSection('game');
                    connectWebSocket();
                } catch (error) {
                    console.error("Falha ao entrar no lobby:", error);
                    alert(`Não foi possível entrar no lobby: ${error.message}`);
                }
            });

            lobbyIdDisplay.addEventListener('click', () => {
                if(lobbyId){
                    navigator.clipboard.writeText(lobbyId)
                        .then(() => alert('ID do Lobby copiado para a área de transferência!'))
                        .catch(err => console.error('Erro ao copiar ID do lobby: ', err));
                }
            });

            leaveLobbyBtn.addEventListener('click', () => {
                if (websocket) websocket.close();
                lobbyId = null; clientId = null; currentLobbyState = {};
                localStorage.removeItem('myClientIdAtLobbyCreation');
                localStorage.removeItem('player1OriginalName');
                localStorage.removeItem('player2OriginalName');
                showSection('lobbyActions'); // Volta para tela de criar/entrar
            });

            playAgainBtn.addEventListener('click', () => {
                if (currentLobbyState.game_state === 'results' && websocket && websocket.readyState === WebSocket.OPEN && currentLobbyState.song_id) {
                    // Envia pedido de revanche para o servidor (apenas o host pode realmente iniciar)
                    console.log("Solicitando revanche/jogar novamente...")
                    websocket.send(JSON.stringify({ type: "request_rematch_or_new_song", data: { action: "rematch" } }));
                }
            });

            newGameBtn.addEventListener('click', () => {
                if (currentLobbyState.game_state === 'results' && websocket && websocket.readyState === WebSocket.OPEN) {
                    // Envia pedido de nova música para o servidor (apenas o host pode realmente iniciar)
                    console.log("Solicitando nova música...")
                    websocket.send(JSON.stringify({ type: "request_rematch_or_new_song", data: { action: "new_song" } }));
                }
            });

            // Adicionar listener para o evento onended do audioPlayer
            audioPlayer.onended = () => {
                const clientIsActually = clientId; // Para ter certeza qual cliente está logando
                console.log(`[Audio Ended - Event Handler - Cliente ${clientIsActually}] Evento 'onended' DISPARADO.`);
                console.log(`[Audio Ended - Event Handler - Cliente ${clientIsActually}] Estado IMEDIATO do audioPlayer: Paused=${audioPlayer.paused}, Ended=${audioPlayer.ended}, CurrentTime=${audioPlayer.currentTime}, Duration=${audioPlayer.duration}, ReadyState=${audioPlayer.readyState}, Error=${JSON.stringify(audioPlayer.error)}, Seeking=${audioPlayer.seeking}, Src=${audioPlayer.src}`);

                const currentGameState = currentLobbyState.game_state;
                const isDuetActive = currentLobbyState.duet_mode === true;

                if (isDuetActive && currentGameState === 'duet_performance') {
                    // Verificar se a música realmente terminou (currentTime próximo do fim)
                    // Isso é importante para evitar paradas falsas quando o outro jogador para
                    const isReallyAtTheEnd = audioPlayer.currentTime > 0 &&
                                             audioPlayer.duration > 0 &&
                                             (audioPlayer.currentTime >= audioPlayer.duration - 1.5);

                    console.log(`[Audio Ended - Dueto - Cliente ${clientIsActually}] Verificação de fim real: currentTime=${audioPlayer.currentTime}, duration=${audioPlayer.duration}, isReallyAtTheEnd=${isReallyAtTheEnd}`);

                    if (!isReallyAtTheEnd) {
                        console.log(`[Audio Ended - Dueto - Cliente ${clientIsActually}] Detectamos um 'fim' FALSO (a música não terminou realmente). Ignorando evento.`);
                        // Tentar restaurar a reprodução se possível
                        try {
                            if (audioPlayer.currentTime < audioPlayer.duration - 1.5) {
                                console.log(`[Audio Ended - Dueto - Cliente ${clientIsActually}] Tentando restaurar reprodução após falso fim.`);
                                audioPlayer.play().catch(e => console.error("Erro ao tentar restaurar reprodução:", e));
                            }
                        } catch (e) {
                            console.error(`[Audio Ended - Dueto - Cliente ${clientIsActually}] Erro ao tentar restaurar reprodução:`, e);
                        }
                        return; // Sair sem parar a gravação
                    }

                    console.log(`[Audio Ended - Dueto - Cliente ${clientIsActually}] Música terminou PARA MIM (dentro do if).`);
                    // Só para a gravação se ela ainda estiver ativa
                    if (myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording') {
                        console.log(`[Audio Ended - Dueto - Cliente ${clientIsActually}] Gravador estava ativo. Parando gravação.`);
                        setTimeout(() => {
                            // Adicionar log antes de chamar stopRecordingAndSubmit
                            console.log(`[Audio Ended - Dueto - Cliente ${clientIsActually}] Timeout executado. Chamando stopRecordingAndSubmit() para o MediaRecorder state: ${myActiveDuetMediaRecorder?.state}`);
                            stopRecordingAndSubmit();
                        }, 300); // Delay para capturar últimos chunks
                    } else {
                        console.log(`[Audio Ended - Dueto - Cliente ${clientIsActually}] Gravador NÃO estava ativo (estado: ${myActiveDuetMediaRecorder?.state}). Não chamando stopRecordingAndSubmit.`);
                    }
                } else if (!isDuetActive && (currentGameState === 'player1_turn' || currentGameState === 'player2_turn')) {
                    // Código existente para modo revezamento
                    if (currentLobbyState.turn_client_id === clientIsActually && mediaRecorder && mediaRecorder.state === "recording"){
                        console.log(`[Audio Ended - Revezamento - Cliente ${clientIsActually}] Áudio terminou no MEU turno. Parando e submetendo.`);
                        stopRecordingAndSubmit();
                    }
                } else {
                    console.log(`[Audio Ended - Event Handler - Cliente ${clientIsActually}] Condições do IF/ELSE IF não atendidas. DuetActive: ${isDuetActive}, GameState: ${currentGameState}`);
                }
            };

            // Adicionar um botão de "Parada Forçada" para emergências
            function addEmergencyStopButton() {
                // Adicionar antes do stopBtn
                const emergencyBtn = document.createElement('button');
                emergencyBtn.id = 'emergencyStopBtn';
                emergencyBtn.className = 'btn btn-danger btn-lg me-2';
                emergencyBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Parada Forçada';
                emergencyBtn.style.display = 'none';

                // Inserir antes do stopBtn
                stopBtn.parentNode.insertBefore(emergencyBtn, stopBtn);

                // Adicionar evento de clique
                emergencyBtn.addEventListener('click', () => {
                    console.log('[Emergency Stop] Botão de parada forçada clicado.');
                    if (myActiveDuetMediaRecorder) {
                        // Força a parada mesmo que o estado pareça inativo
                        try {
                            myActiveDuetMediaRecorder.stop();
                        } catch (e) {
                            console.error('[Emergency Stop] Erro ao parar myActiveDuetMediaRecorder:', e);
                        }
                    }

                    // Reset UI
                    emergencyBtn.style.display = 'none';
                    recordBtn.disabled = false;
                    recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                    recordBtn.style.display = 'inline-block';
                    stopBtn.style.display = 'none';
                });

                return emergencyBtn;
            }

            // Modificar setupAndStartRecordingDuet para mostrar o botão de emergência
            async function setupAndStartRecordingDuet() {
                // ... código existente ...

                // Mostrar botão de emergência após 5 segundos de gravação
                let emergencyBtn = document.getElementById('emergencyStopBtn');
                if (!emergencyBtn) {
                    emergencyBtn = addEmergencyStopButton();
                }

                setTimeout(() => {
                    if (myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording') {
                        emergencyBtn.style.display = 'inline-block';
                    }
                }, 5000);

                // ... resto do código existente ...
            }

            // Função para obter o ID do cliente do turno atual (exemplo, adapte conforme sua lógica de estado)
            // ... existing code ...
        });

        // <<< Função para Parsear LRC >>>
        function parseLRC(lrcContent) {
            const lines = lrcContent.split('\n');
            const result = [];
            // Regex para capturar [mm:ss.xx] ou [mm:ss] e o texto
            const timeRegex = /^\[(\d{2}):(\d{2})(?:[\.\:](\d{1,3}))?\](.*)/;

            for (const line of lines) {
                const match = line.trim().match(timeRegex);
                if (match) {
                    const minutes = parseInt(match[1], 10);
                    const seconds = parseInt(match[2], 10);
                    // Pad centiseconds/milliseconds to 3 digits if needed
                    let milliseconds = parseInt(match[3] || '0', 10);
                    if (match[3] && match[3].length === 2) milliseconds *= 10; // Convert centiseconds to milliseconds
                    if (match[3] && match[3].length === 1) milliseconds *= 100;

                    const time = minutes * 60 + seconds + milliseconds / 1000;
                    const text = match[4].trim();
                    if (text) { // Ignora linhas de timestamp sem texto
                         result.push({ time, text });
                    }
                }
            }
            // Ordena por tempo, caso o LRC não esteja ordenado
            result.sort((a, b) => a.time - b.time);
            return result;
        }

        // <<< Função para Atualizar Destaque da Letra >>>
        function syncLyrics() {
            if (!parsedLRC || parsedLRC.length === 0 || !audioPlayer) return;
            const currentTime = audioPlayer.currentTime;
            let newCurrentLineIndex = -1;

            // Encontra o índice da última linha cujo tempo é menor ou igual ao tempo atual
            for (let i = 0; i < parsedLRC.length; i++) {
                if (parsedLRC[i].time <= currentTime) {
                    newCurrentLineIndex = i;
                } else {
                    break; // Para assim que encontrar uma linha com tempo maior
                }
            }

            if (newCurrentLineIndex !== currentLineIndex) {
                 console.log(`[Sync] Time: ${currentTime.toFixed(2)}s -> Line: ${newCurrentLineIndex} (${parsedLRC[newCurrentLineIndex]?.text})`);
                const linesElements = lyricsDisplay.children;
                if (currentLineIndex >= 0 && linesElements[currentLineIndex]) {
                    linesElements[currentLineIndex].classList.remove('current-line');
                }
                if (newCurrentLineIndex >= 0 && linesElements[newCurrentLineIndex]) {
                    const currentLineElement = linesElements[newCurrentLineIndex];
                    currentLineElement.classList.add('current-line');
                    // Auto-scroll
                    const containerRect = lyricsDisplay.getBoundingClientRect();
                    const lineRect = currentLineElement.getBoundingClientRect();
                    // Scroll se a linha estiver fora da área visível (considerando um pouco de margem)
                    if (lineRect.bottom > containerRect.bottom - 10 || lineRect.top < containerRect.top + 10) {
                        currentLineElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
                currentLineIndex = newCurrentLineIndex;
            }
        }

        // <<< Função para Carregar e Preparar Música e Letras >>>
        async function loadAndPrepareSong(songDetails, songId) {
             console.log("[Song Load] Preparando música e letras para songId:", songId, songDetails);
             // Armazena o ID da música atual no player para evitar recargas desnecessárias
             audioPlayer.dataset.currentSongId = songId;
             lyricsDisplay.innerHTML = '<p class="text-center p-3">Carregando letra...</p>';
             parsedLRC = [];
             currentLineIndex = -1;
             audioPlayer.removeEventListener('timeupdate', syncLyrics);
             audioPlayer.pause();
             audioPlayer.currentTime = 0;
             audioPlayer.src = ''; // Limpa source antigo

             if (!songDetails || !songDetails.audio_url || !songDetails.lrc_url) {
                 lyricsDisplay.innerHTML = '<p class="text-center p-3 text-danger">Erro: Dados de áudio ou LRC ausentes.</p>';
                 console.error("[Song Load] URLs de áudio ou LRC ausentes", songDetails);
                 return;
             }

             // Define a URL do áudio
             audioPlayer.src = songDetails.audio_url;
             audioPlayer.load(); // Inicia o carregamento do áudio

            // Busca e parseia o LRC
            // Cancela fetch anterior se houver
            if(lrcFetchController) {
                 lrcFetchController.abort();
            }
            lrcFetchController = new AbortController();
            const signal = lrcFetchController.signal;

            try {
                console.log(`[Song Load] Buscando LRC de ${songDetails.lrc_url}`);
                const response = await fetch(songDetails.lrc_url, { signal });
                if (!response.ok) throw new Error(`Falha ao buscar LRC: ${response.statusText}`);
                const lrcText = await response.text();
                console.log("[Song Load] LRC recebido, parseando...");
                parsedLRC = parseLRC(lrcText);
                console.log(`[Song Load] ${parsedLRC.length} linhas de LRC parseadas.`);

                // Popula a div de letras com as linhas parseadas
                lyricsDisplay.innerHTML = ''; // Limpa mensagem de carregando
                if (parsedLRC.length > 0) {
                    parsedLRC.forEach((line, index) => {
                        const lineElement = document.createElement('div');
                        lineElement.textContent = line.text;
                        lineElement.classList.add('lyrics-line');
                        lineElement.dataset.time = line.time;
                        lyricsDisplay.appendChild(lineElement);
                    });
                    // Adiciona o listener APÓS carregar o LRC
                    audioPlayer.addEventListener('timeupdate', syncLyrics);
                } else {
                     lyricsDisplay.innerHTML = '<p class="text-center p-3 text-warning">Letra sincronizada não disponível ou vazia.</p>';
                }

            } catch (error) {
                if (error.name === 'AbortError') {
                     console.log("[Song Load] Fetch de LRC cancelado.");
                 } else {
                     console.error("[Song Load] Erro ao carregar/parsear LRC:", error);
                     lyricsDisplay.innerHTML = '<p class="text-center p-3 text-danger">Erro ao carregar letra sincronizada.</p>';
                 }
                 parsedLRC = []; // Garante que está vazio se falhar
            } finally {
                 lrcFetchController = null; // Limpa o controller
            }
        }

        // <<< FUNÇÕES PARA GERENCIAR MONITORAMENTO >>>
        function initAudioContextIfNeeded() {
            if (!audioContext) {
                try {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    console.log('[Monitor] AudioContext inicializado.');
                } catch (e) {
                    console.error('[Monitor] Falha ao criar AudioContext:', e);
                    alert('Seu navegador não suporta Web Audio API, monitoramento indisponível.');
                    monitorBtn.disabled = true; // Desabilitar botão se falhar
                    return false;
                }
            }
            return true;
        }

        function startMonitoring() {
            if (!microphoneStream || !microphoneStream.active) {
                console.warn('[Monitor] Tentativa de iniciar monitoramento sem stream de microfone ativo.');
                alert('Microfone não está ativo. Inicie a gravação primeiro.');
                return;
            }
            if (isMonitoring) {
                console.log('[Monitor] Monitoramento já está ativo.');
                return; // Já está monitorando
            }
            if (!initAudioContextIfNeeded()) return; // Falha ao iniciar contexto

            try {
                console.log('[Monitor] Iniciando monitoramento...');
                microphoneSourceNode = audioContext.createMediaStreamSource(microphoneStream);
                monitorGainNode = audioContext.createGain();

                // Conectar via updateAudioGraph para incluir efeitos se necessário
                updateAudioGraph();

                isMonitoring = true;
                monitorBtn.innerHTML = '<i class="fas fa-ear-listen"></i> Parar Retorno';
                monitorBtn.classList.remove('btn-outline-secondary');
                monitorBtn.classList.add('btn-secondary', 'active');
                enableEffectsCheckbox.disabled = false; // <<< Habilitar checkbox de efeitos
                console.log('[Monitor] Monitoramento ATIVADO.');

            } catch (error) {
                console.error('[Monitor] Erro ao iniciar monitoramento:', error);
                alert('Erro ao tentar iniciar o retorno de áudio.');
                stopMonitoring(); // Tentar limpar se algo falhou
            }
        }

        function stopMonitoring() {
            if (!isMonitoring) return;

            console.log('[Monitor] Parando monitoramento...');
            // Desconectar e limpar nós de efeitos e monitoramento via updateAudioGraph
            isMonitoring = false; // Setar antes para updateAudioGraph desconectar tudo
            updateAudioGraph();

            // --- NÃO PARAR O STREAM AQUI ---
            // A responsabilidade de parar as tracks é do código que iniciou o stream (onstop do recorder)
            /*
            if (microphoneStream) {
                 microphoneStream.getTracks().forEach(track => track.stop()); // NÃO FAZER AQUI
                 microphoneStream = null; // NÃO ANULAR AQUI
            }
            */
            // --- Fim da remoção ---

            // Resetar UI e estado dos efeitos
            monitorBtn.innerHTML = '<i class="fas fa-ear-listen"></i> Ouvir Retorno';
            monitorBtn.classList.add('btn-outline-secondary');
            monitorBtn.classList.remove('btn-secondary', 'active');
            enableEffectsCheckbox.disabled = true;
            enableEffectsCheckbox.checked = false; // Desmarcar checkbox
            effectsEnabled = false;
            effectsBox.style.display = 'none';
            effectSelect.value = 'none';
            selectedEffect = 'none';
            console.log('[Monitor] Monitoramento DESATIVADO e efeitos resetados.');
        }

        // <<< FUNÇÃO PARA ATUALIZAR CONEXÕES DE ÁUDIO (GRAFO) >>>
        function updateAudioGraph() {
            // Adicionado log extra para checar nós
            if (!audioContext || !microphoneSourceNode || !monitorGainNode) {
                console.error('[Audio Graph] ERRO CRÍTICO: Contexto ou nós essenciais (micSource, monitorGain) não estão prontos!');
                // Limpar conexões anteriores como segurança se algo não estiver pronto
                if (microphoneSourceNode) microphoneSourceNode.disconnect();
                if (delayNode) delayNode.disconnect();
                if (monitorGainNode) monitorGainNode.disconnect();
                return;
            }

            console.log(`[Audio Graph] Atualizando: Monitorando=${isMonitoring}, Efeitos=${effectsEnabled}, Efeito=${selectedEffect}`);

            // 1. Limpar conexões existentes para evitar duplicação
            microphoneSourceNode.disconnect();
            if (delayNode) delayNode.disconnect();
            if (filterNode) filterNode.disconnect();     // <<< Limpar nó de filtro
            if (distortionNode) distortionNode.disconnect(); // <<< Limpar nó de distorção
            // Não desconectamos monitorGainNode da destination ainda

            // 2. Destruir nós de efeitos antigos para recriar se necessário
            if (delayNode) delayNode = null;
            if (filterNode) filterNode = null;     // <<< Limpar nó de filtro
            if (distortionNode) distortionNode = null; // <<< Limpar nó de distorção
            console.log('[Audio Graph] Nós de efeitos anteriores limpos.');

            // 3. Reconectar com base no estado atual
            if (isMonitoring) {
                let currentNode = microphoneSourceNode;

                if (effectsEnabled && selectedEffect !== 'none') {
                    console.log(`[Audio Graph] Habilitando efeito: ${selectedEffect}`);
                    if (selectedEffect === 'delay') {
                        console.log('[Audio Graph] Criando e configurando DelayNode...');
                        // Aumentar delay e feedback para tornar mais óbvio
                        delayNode = audioContext.createDelay(0.6); // Delay de 0.6 segundos
                        const feedback = audioContext.createGain();
                        feedback.gain.value = 0.6; // Feedback mais forte

                        console.log('[Audio Graph] Conectando: Mic -> Delay');
                        currentNode.connect(delayNode);
                        console.log('[Audio Graph] Conectando: Delay -> Feedback');
                        delayNode.connect(feedback);
                        console.log('[Audio Graph] Conectando: Feedback -> Delay');
                        feedback.connect(delayNode); // Conecta de volta ao delay

                        currentNode = delayNode;
                        console.log('[Audio Graph] Nó atual agora é DelayNode');
                    }
                    // <<< Adicionar lógica para novos efeitos >>>
                    else if (selectedEffect === 'lowpass' || selectedEffect === 'highpass') {
                        console.log(`[Audio Graph] Criando BiquadFilterNode (${selectedEffect})...`);
                        filterNode = audioContext.createBiquadFilter();
                        filterNode.type = selectedEffect; // 'lowpass' ou 'highpass'
                        if (selectedEffect === 'lowpass') {
                            filterNode.frequency.setValueAtTime(800, audioContext.currentTime); // Corta acima de 800 Hz
                        } else { // highpass
                            filterNode.frequency.setValueAtTime(1000, audioContext.currentTime); // Corta abaixo de 1000 Hz
                        }
                        filterNode.Q.setValueAtTime(1, audioContext.currentTime); // Fator Q padrão

                        console.log('[Audio Graph] Conectando: Mic -> Filtro');
                        currentNode.connect(filterNode);
                        currentNode = filterNode;
                        console.log('[Audio Graph] Nó atual agora é BiquadFilterNode');
                    }
                    else if (selectedEffect === 'distortion') {
                        console.log('[Audio Graph] Criando WaveShaperNode (Distorção)...');
                        distortionNode = audioContext.createWaveShaper();

                        // Criar curva de distorção
                        const amount = 40; // Ajuste para mais/menos distorção
                        const n_samples = 44100;
                        const curve = new Float32Array(n_samples);
                        const deg = Math.PI / 180;
                        for (let i = 0; i < n_samples; ++i) {
                            const x = i * 2 / n_samples - 1;
                            curve[i] = (3 + amount) * x * 20 * deg / (Math.PI + amount * Math.abs(x));
                        }
                        distortionNode.curve = curve;
                        distortionNode.oversample = '4x'; // Melhora qualidade

                        console.log('[Audio Graph] Conectando: Mic -> Distorção');
                        currentNode.connect(distortionNode);
                        currentNode = distortionNode;
                        console.log('[Audio Graph] Nó atual agora é WaveShaperNode');
                    }
                    // Adicionar else if para outros efeitos aqui
                } else {
                    console.log('[Audio Graph] Efeitos não habilitados ou nenhum selecionado.'); // <<< LOG
                }

                // Conectar o último nó da cadeia (seja o microfone ou o efeito) ao ganho final
                console.log(`[Audio Graph] Conectando nó final (${currentNode.constructor.name}) -> monitorGainNode`); // <<< LOG
                currentNode.connect(monitorGainNode);

                // Garantir que o ganho final esteja conectado ao destino
                // Conectamos apenas uma vez para evitar múltiplos áudios
                try {
                     monitorGainNode.disconnect(audioContext.destination);
                } catch(e) { /* Ignora erro se não estava conectado */ }
                monitorGainNode.connect(audioContext.destination);

            } else {
                // Se não está monitorando, garantir que tudo esteja desconectado do destino
                console.log('[Audio Graph] Monitoramento desligado, desconectando tudo.');
                 try { monitorGainNode.disconnect(audioContext.destination); } catch(e) {}
                 // Os nós individuais já foram desconectados acima
            }
            console.log('[Audio Graph] Grafo de áudio atualizado.');
        }
        // <<< FIM DAS FUNÇÕES DE EFEITOS >>>

        // <<< Adicionar Listeners para os controles de Efeitos >>>
        enableEffectsCheckbox.addEventListener('change', (event) => {
            effectsEnabled = event.target.checked;
            console.log(`[Effect Control] Checkbox alterado. effectsEnabled: ${effectsEnabled}`);

            // Controlar visibilidade e estado da caixa de seleção
            effectSelect.disabled = !effectsEnabled;

            if (effectsEnabled) {
                effectsBox.style.display = 'inline-block'; // Mostrar a div do select
            } else {
                effectsBox.style.display = 'none';     // Ocultar a div do select
                effectSelect.value = 'none';         // Resetar para "Nenhum"
                selectedEffect = 'none';             // Atualizar estado interno
            }
            updateAudioGraph(); // Reconectar áudio com ou sem efeitos
        });

        effectSelect.addEventListener('change', (event) => {
            selectedEffect = event.target.value;
            console.log(`[Effect Control] Select alterado. selectedEffect: ${selectedEffect}`); // <<< LOG
            updateAudioGraph(); // Reconectar áudio com o novo efeito (ou nenhum)
        });

        // <<< NOVA FUNÇÃO PARA CONFIGURAR E INICIAR GRAVAÇÃO (MODO DUETO) >>>
        async function setupAndStartRecordingDuet() {
            const localClientId = clientId; // Captura clientId para esta execução
            console.log(`[Mic Setup - Dueto - Cliente ${localClientId}] Iniciando...`);

            // --- Limpeza Preventiva REMOVIDA para evitar race conditions ---
            /*
            if (myActiveDuetMediaRecorder) { ... }
            if (myActiveDuetStream) { ... }
            */
            // --- Fim da Remoção ---

            if (audioContext && audioContext.state === 'suspended') {
                await audioContext.resume();
            }

            let streamForThisSession = null; // Variável local para o stream desta sessão
            let recorderForThisSession = null; // Variável local para o recorder desta sessão
            let chunksForThisSession = []; // Chunks locais para esta sessão

            try {
                console.log(`[Mic Setup - Dueto - Cliente ${localClientId}] Configurando microfone...`);
                streamForThisSession = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 44100,
                        channelCount: 1,
                        echoCancellation: false,
                        noiseSuppression: false,
                        autoGainControl: false
                    }
                });
                // Atribui à global APÓS sucesso
                myActiveDuetStream = streamForThisSession;
                console.log(`[Mic Setup - Dueto - Cliente ${localClientId}] Stream desta sessão obtido (ID: ${streamForThisSession.id}). Atribuído a myActiveDuetStream.`);
                // <<< ATRIBUIR TAMBÉM À VARIÁVEL GLOBAL DE MONITORAMENTO >>>
                microphoneStream = streamForThisSession;
                console.log(`[Mic Setup - Dueto - Cliente ${localClientId}] Stream também atribuído a microphoneStream para monitoramento.`);

                // Adiciona listeners à track do stream local
                const audioTracks = streamForThisSession.getAudioTracks();
                if (audioTracks.length > 0) {
                    // ... (código dos listeners onended/onmute da track permanece o mesmo) ...
                    const currentTrack = audioTracks[0];
                    const trackClientId = localClientId;
                    console.log(`[Mic Setup - Dueto - Cliente ${trackClientId}] Adicionando listeners à track de áudio ID: ${currentTrack.id} (Stream ID: ${streamForThisSession.id})`);
                    currentTrack.onended = () => {
                        console.warn(`[MediaStreamTrack Listener - Cliente ${trackClientId}] Evento 'onended' DISPARADO para a track de áudio (ID: ${currentTrack.id})!`);
                        console.warn(`[MediaStreamTrack Listener - Cliente ${trackClientId}] Estado do MediaRecorder (global) neste momento: ${myActiveDuetMediaRecorder?.state}`); // Log estado do global
                    };
                    currentTrack.onmute = () => { console.warn(`[MediaStreamTrack Listener - Cliente ${trackClientId}] Evento 'onmute' DISPARADO para a track de áudio (ID: ${currentTrack.id})!`); };
                    currentTrack.onunmute = () => { console.warn(`[MediaStreamTrack Listener - Cliente ${trackClientId}] Evento 'onunmute' DISPARADO para a track de áudio (ID: ${currentTrack.id})!`); };
                } else {
                    console.error(`[Mic Setup - Dueto - ${localClientId}] Nenhuma track de áudio encontrada em streamForThisSession!`);
                }

                // Cria recorder local
                recorderForThisSession = new MediaRecorder(streamForThisSession);
                // Atribui à global APÓS sucesso
                myActiveDuetMediaRecorder = recorderForThisSession;
                console.log(`[Mic Setup - Dueto - Cliente ${localClientId}] MediaRecorder desta sessão criado. Atribuído a myActiveDuetMediaRecorder.`);

                // Adiciona listeners ao recorder local
                recorderForThisSession.onerror = (event) => {
                    // ... (código do listener onerror permanece o mesmo) ...
                    const errorClientId = localClientId;
                    console.error(`[MediaRecorder onerror - Dueto - Cliente ${errorClientId}] !!!!! ERRO DETECTADO NO MEDIARECORDER !!!!!`, event);
                    if (event.error) {
                        console.error(`[MediaRecorder onerror - Dueto - Cliente ${errorClientId}] Detalhes do Erro:`, event.error);
                        console.error(`[MediaRecorder onerror - Dueto - Cliente ${errorClientId}] Nome do Erro: ${event.error.name}, Mensagem: ${event.error.message}`);
                    }
                };

                recorderForThisSession.addEventListener('dataavailable', event => {
                    const dataClientId = localClientId;
                    console.log(`[MediaRecorder dataavailable - Dueto - Cliente ${dataClientId}] Chunk size: ${event.data.size}`);
                    if (event.data.size > 0) {
                      chunksForThisSession.push(event.data); // Adiciona aos chunks LOCAIS
                    } else {
                      console.warn(`[MediaRecorder dataavailable - Dueto - Cliente ${dataClientId}] Received empty chunk (size 0).`);
                    }
                });

                recorderForThisSession.addEventListener('stop', () => {
                    // !!! Este listener usa as variáveis locais capturadas pela closure !!!
                    const thisRecorderClientIdOnStop = localClientId;
                    const thisStream = streamForThisSession;
                    const thisAudioChunks = chunksForThisSession;

                    console.log(`[Recording - Dueto - ONSTOP] Evento 'stop' para recorder da sessão (cliente: ${thisRecorderClientIdOnStop}).`);

                    // <<< PARAR VISUALIZADOR PARA DUETO >>>
                    stopVisualizerWithRecording(false);

                    if (thisStream) {
                        thisStream.getTracks().forEach(track => track.stop());
                        console.log(`[Mic Cleanup - Dueto - ${thisRecorderClientIdOnStop}] Tracks do stream DESTA SESSÃO paradas.`);
                    }

                    if (thisAudioChunks.length === 0) {
                        console.warn(`[Recording - Dueto - ${thisRecorderClientIdOnStop}] Nenhum dado de áudio gravado (chunks locais). Não enviando.`);
                        // Resetar UI
                        recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                        recordBtn.disabled = false;
                        recordBtn.style.display = 'inline-block';
                        stopBtn.style.display = 'none';
                        audioLoadingIndicator.style.display = 'none';
                        // Limpa a ref global SE AINDA for este recorder
                        if (myActiveDuetMediaRecorder === recorderForThisSession) {
                             myActiveDuetMediaRecorder = null;
                             if(myActiveDuetStream === thisStream) myActiveDuetStream = null;
                        }
                        return;
                    }
                    const audioBlob = new Blob(thisAudioChunks, { type: recorderForThisSession?.mimeType || 'audio/webm' });
                    const reader = new FileReader();
                    reader.readAsDataURL(audioBlob);
                    reader.onloadend = () => {
                        const base64Audio = reader.result;
                        const songIdForSubmission = currentLobbyState.song_id;
                        console.log(`[Recording - Dueto - ${thisRecorderClientIdOnStop}] Pronto para enviar. SongId: ${songIdForSubmission}, Chunks (locais): ${thisAudioChunks.length}`);

                        if (websocket && websocket.readyState === WebSocket.OPEN) {
                            // ... (lógica de envio de audio_submission) ...
                            sendMessage({
                                type: "audio_submission",
                                data: { audio_data: base64Audio, song_id_for_audio: songIdForSubmission }
                            });
                            console.log(`[Recording - Dueto - ${thisRecorderClientIdOnStop}] audio_submission enviado com song_id:`, songIdForSubmission);

                            // <<< REGISTRAR TIMESTAMP DO ENVIO DE ÁUDIO >>>
                            myAudioSubmissionTimestamp = Date.now();
                            console.log(`[Recording - Dueto - ${thisRecorderClientIdOnStop}] Timestamp do envio registrado: ${myAudioSubmissionTimestamp}`);

                            audioLoadingIndicator.style.display = 'block';
                            recordBtn.style.display = 'none';
                            stopBtn.style.display = 'none';
                        } else {
                           // ... (lógica de erro de websocket) ...
                            console.error(`[Recording - Dueto - ${thisRecorderClientIdOnStop}] WebSocket não aberto. State: ${websocket?.readyState}.`);
                            alert("Erro de conexão ao tentar enviar o áudio. Tente novamente.");
                            recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                            recordBtn.disabled = false;
                            recordBtn.style.display = 'inline-block';
                            stopBtn.style.display = 'none';
                            audioLoadingIndicator.style.display = 'none';
                        }
                        // Limpar o gravador global APENAS SE ele ainda for este gravador
                        if (myActiveDuetMediaRecorder === recorderForThisSession) {
                            console.log(`[Recording - Dueto - ONSTOP - Cliente ${thisRecorderClientIdOnStop}] Limpando referência global myActiveDuetMediaRecorder após envio/erro.`);
                            myActiveDuetMediaRecorder = null;
                            if(myActiveDuetStream === thisStream) myActiveDuetStream = null; // Também limpa o stream global
                        } else {
                             console.warn(`[Recording - Dueto - ONSTOP - Cliente ${thisRecorderClientIdOnStop}] Referência global myActiveDuetMediaRecorder NÃO apontava mais para este gravador após envio/erro. Não limpando.`);
                        }
                    };
                });

                // Inicia o recorder local
                recorderForThisSession.start(1000);
                console.log(`[Recording - Dueto - Cliente ${localClientId}] Gravação com recorderForThisSession iniciada.`);
                // Atualiza UI
                monitorBtn.disabled = false; // <<< HABILITAR BOTÃO DE MONITORAMENTO AQUI >>>
                recordBtn.innerHTML = '<span class="recording-indicator"></span><i class="fas fa-microphone-alt me-2"></i>Gravando Dueto...';
                recordBtn.disabled = true;
                stopBtn.style.display = 'inline-block';
                stopBtn.innerHTML = '<span class="recording-indicator"></span><i class="fas fa-stop-circle me-2"></i>Parar Gravação (Dueto)';

                // <<< INICIAR VISUALIZADOR PARA DUETO >>>
                startVisualizerWithRecording(false);

            } catch (error) {
                console.error(`[Mic Setup - Dueto - Cliente ${localClientId}] Falha ao configurar ou iniciar microfone:`, error);
                alert(error.message || "Falha ao iniciar gravação"); // Simplificado
                // Limpeza em caso de falha
                if (streamForThisSession) {
                    streamForThisSession.getTracks().forEach(track => track.stop());
                }
                // Limpa refs globais se elas apontarem para as instâncias locais que falharam
                 if (myActiveDuetStream === streamForThisSession) myActiveDuetStream = null;
                 if (myActiveDuetMediaRecorder === recorderForThisSession) myActiveDuetMediaRecorder = null;
                // Reset UI
                recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                recordBtn.disabled = false;
                recordBtn.style.display = 'inline-block';
                stopBtn.style.display = 'none';
                return; // Retorna em caso de erro
            }
        }

        // <<< REVISED FUNCTION TO START RECORDING BEFORE PLAYING >>>
        async function playAndRecordForDuet() {
            const localClientId = clientId; // Capture client ID for logs
            console.log(`[Dueto - Cliente ${localClientId}] playAndRecordForDuet chamado.`);

            // 1. Setup and start recording FIRST
            console.log(`[Dueto - Cliente ${localClientId}] Chamando setupAndStartRecordingDuet ANTES de tocar música.`);
            let recordingSetupSuccess = false;
            try {
                await setupAndStartRecordingDuet(); // Espera o setup (getUserMedia, recorder.start())
                console.log(`[Dueto - Cliente ${localClientId}] setupAndStartRecordingDuet finalizado com sucesso.`);
                recordingSetupSuccess = true;
            } catch (error) {
                // setupAndStartRecordingDuet já deve ter lidado com o alerta e UI reset
                console.error(`[Dueto - Cliente ${localClientId}] Falha durante setupAndStartRecordingDuet:`, error);
                // Resetar UI do botão "Começar Dueto" se falhar aqui (garantia)
                recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                recordBtn.disabled = false;
                recordingSetupSuccess = false;
            }

            // 2. Play music ONLY if recording setup was successful
            if (recordingSetupSuccess) {
                if (audioPlayer && audioPlayer.src) {
                    console.log(`[Dueto - Cliente ${localClientId}] Gravação iniciada. Tentando iniciar a música...`);
                    audioPlayer.currentTime = 0;
                    try {
                        await audioPlayer.play();
                        console.log(`[Dueto - Cliente ${localClientId}] Música iniciada (play() resolvido).`);
                    } catch (error) {
                        console.error(`[Dueto - Cliente ${localClientId}] Erro ao tocar áudio após iniciar gravação:`, error);
                        alert("Erro ao iniciar a música para o dueto. A gravação pode ter começado. Verifique o console.");
                        // A gravação já começou, mas a música não. O usuário pode ter que parar manualmente?
                        // Ou tentamos parar a gravação automaticamente aqui?
                        // Vamos parar a gravação se a música falhar em tocar.
                        if (myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording') {
                             console.warn(`[Dueto - Cliente ${localClientId}] Parando gravação porque a música falhou ao iniciar.`);
                             stopRecordingAndSubmit(); // Usa a função unificada
                        }
                        // Resetar botão
                         recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                         recordBtn.disabled = false;
                    }
                } else {
                    console.error(`[Dueto - Cliente ${localClientId}] Gravação iniciada, mas Áudio do player não está pronto!`);
                    alert("Erro: Música não carregada para o dueto, mas a gravação pode ter começado. Avise o host e pare a gravação.");
                    // Deixar a gravação ativa para o usuário parar? Ou parar aqui?
                     if (myActiveDuetMediaRecorder && myActiveDuetMediaRecorder.state === 'recording') {
                         console.warn(`[Dueto - Cliente ${localClientId}] Parando gravação porque a música não estava pronta.`);
                         stopRecordingAndSubmit();
                     }
                     recordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar Dueto';
                     recordBtn.disabled = false;
                }
            } else {
                 console.log(`[Dueto - Cliente ${localClientId}] Música NÃO será iniciada porque a configuração da gravação falhou.`);
            }
        }

        // <<< LÓGICA PARA O MODO SOLO >>>
        startSoloGameBtn.addEventListener('click', async () => { // Tornar async
            if (!playerName) {
                alert('Por favor, defina seu nome primeiro.');
                showSection('initialSetup');
                return;
            }
            console.log("Botão 'Iniciar Jogo Solo' clicado");
            currentSoloSongId = null; // Reseta a música selecionada
            showSection('soloGame');
            // A função loadAndDisplaySongs já deve ter sido chamada no DOMContentLoaded
            // e populado songsData. Aqui garantimos que a lista de músicas solo seja exibida.
            populateSoloSongList();
            soloSongSelectionArea.style.display = 'block';
            soloGamePlayArea.style.display = 'none';
            soloResultsArea.style.display = 'none';
            if (document.getElementById('main-title-welcome')) {
                document.getElementById('main-title-welcome').textContent = `Modo Solo - ${playerName}`;
            }
        });

        exitSoloModeBtn.addEventListener('click', () => {
            stopSoloMicrophone(); // Garante que o microfone seja liberado
            resetSoloGameUI();
            showSection('lobbyActions');
             if (document.getElementById('main-title-welcome') && playerName) {
                document.getElementById('main-title-welcome').textContent = `Bem-vindo(a), ${playerName}!`;
            } else if (document.getElementById('main-title-welcome')) {
                document.getElementById('main-title-welcome').textContent = `Bem-vindo ao Karaokê!`;
            }
        });

        // Função para popular a lista de músicas no modo solo
        async function populateSoloSongList() { // Tornar async
            try {
                await ensureSongsDataLoaded(); // Garante que songsData está carregado
            } catch (error) {
                // ensureSongsDataLoaded já logou o erro. Aqui tratamos a UI.
                soloSongList.innerHTML = '<li class="apple-style-list-item text-danger">Falha ao carregar músicas. Verifique o console.</li>';
                return;
            }

            soloSongList.innerHTML = ''; // Limpa a lista anterior
            if (Object.keys(songsData).length === 0) {
                const li = document.createElement('li');
                li.className = 'apple-style-list-item';
                li.textContent = 'Nenhuma música disponível no servidor ou falha ao carregar.';
                soloSongList.appendChild(li);
                return;
            }

            Object.entries(songsData).forEach(([id, song]) => {
                const listItem = document.createElement('li');
                listItem.className = 'apple-style-list-item';
                listItem.dataset.songId = id;
                listItem.innerHTML = `
                    <div class="apple-style-song-info">
                        <span class="apple-style-song-title">${song.titulo}</span>
                        <span class="apple-style-song-artist">${song.artista}</span>
                    </div>
                    <span class="material-icons-outlined apple-style-play-icon">play_circle_filled</span>
                `;
                listItem.addEventListener('click', () => {
                    document.querySelectorAll('#soloSongList .apple-style-list-item').forEach(item => item.classList.remove('active')); // 'active' pode ser uma classe customizada para o item selecionado
                    listItem.classList.add('active');
                    currentSoloSongId = id;
                    startSoloSingingSession(id);
                });
                soloSongList.appendChild(listItem);
            });
        }

        async function startSoloSingingSession(songId) {
            if (!songId || !songsData[songId]) {
                alert("Música inválida selecionada.");
                return;
            }
            const song = songsData[songId];
            currentSoloSongId = songId; // Guarda o ID da música atual

            soloCurrentSongTitle.textContent = song.titulo;
            soloCurrentSongArtist.textContent = song.artista;

            soloSongSelectionArea.style.display = 'none';
            soloGamePlayArea.style.display = 'block';
            soloResultsArea.style.display = 'none';
            soloAudioPlayer.src = song.audio_url || '';
            soloRecordBtn.disabled = !song.audio_url; // Habilita gravação se tiver áudio
            soloMonitorBtn.disabled = false; // Habilita monitoramento

            // Resetar botões e indicadores
            soloRecordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar a Cantar';
            soloRecordBtn.classList.remove('btn-warning');
            soloRecordBtn.classList.add('btn-primary');
            soloStopBtn.style.display = 'none';
            soloAudioLoadingIndicator.style.display = 'none';
            soloLyricsDisplay.innerHTML = 'Carregando letra...';

            // Carregar e exibir letra LRC (ou simples)
            if (song.lrc_url) {
                try {
                    const response = await fetch(song.lrc_url);
                    if (!response.ok) throw new Error(`Falha ao buscar LRC: ${response.statusText}`);
                    const lrcContent = await response.text();

                    // Inicializa o parser LRC para o modo solo
                    soloLrcParser = new LRC();
                    soloLrcParser.load(lrcContent);

                    displaySoloLyrics(soloLrcParser.getLyrics()); // Exibe todas as linhas inicialmente

                    // Sincronizar com o áudio
                    soloAudioPlayer.ontimeupdate = () => {
                        if (soloLrcParser && soloAudioPlayer.currentTime > 0) {
                            const currentIndex = soloLrcParser.select(soloAudioPlayer.currentTime);
                            highlightSoloLyricLine(currentIndex);
                        }
                    };

                } catch (error) {
                    console.error("Erro ao carregar ou processar LRC para modo solo:", error);
                    soloLyricsDisplay.innerHTML = `<p class="text-danger">Não foi possível carregar a letra LRC. Tentando letra simples...</p>`;
                    if (song.letra) { // Fallback para letra simples
                       soloLyricsDisplay.innerHTML = song.letra.replace(/\n/g, '<br>');
                    } else {
                       soloLyricsDisplay.innerHTML = `<p class="text-warning">Letra não disponível.</p>`;
                    }
                }
            } else if (song.letra) {
                soloLyricsDisplay.innerHTML = song.letra.replace(/\n/g, '<br>');
                soloAudioPlayer.ontimeupdate = null; // Remove handler de LRC se não houver LRC
            } else {
                soloLyricsDisplay.innerHTML = '<p class="text-warning">Letra não disponível.</p>';
                soloAudioPlayer.ontimeupdate = null;
            }

            // Configurar botões de gravação para modo solo
            soloRecordBtn.onclick = () => setupAndStartSoloRecording();
            soloStopBtn.onclick = () => stopSoloRecordingAndProcess();
        }

        function displaySoloLyrics(lyricsLines) {
            soloLyricsDisplay.innerHTML = ''; // Limpa anterior
            if (!lyricsLines || lyricsLines.length === 0) {
                soloLyricsDisplay.innerHTML = '<p>Letra não encontrada ou vazia.</p>';
                return;
            }
            lyricsLines.forEach((line, index) => {
                const p = document.createElement('p');
                p.dataset.lineIndex = index;
                p.textContent = line.text;
                soloLyricsDisplay.appendChild(p);
            });
        }

        function highlightSoloLyricLine(currentIndex) {
            const lines = soloLyricsDisplay.querySelectorAll('p[data-line-index]');
            lines.forEach((line, index) => {
                if (index === currentIndex) {
                    line.classList.add('active-lyric');
                    // Scroll para a linha ativa
                    // line.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    // Ajuste para um scroll mais controlado se necessário
                    const displayRect = soloLyricsDisplay.getBoundingClientRect();
                    const lineRect = line.getBoundingClientRect();
                    if (lineRect.bottom > displayRect.bottom || lineRect.top < displayRect.top) {
                       soloLyricsDisplay.scrollTop += (lineRect.top - displayRect.top - (displayRect.height / 2) + (lineRect.height / 2));
                    }
                } else {
                    line.classList.remove('active-lyric');
                }
            });
        }

        async function setupAndStartSoloRecording() {
            if (!currentSoloSongId || !songsData[currentSoloSongId] || !songsData[currentSoloSongId].audio_url) {
                alert("Nenhuma música com áudio selecionada para gravar.");
                return;
            }

            try {
                // Se já estiver monitorando, reutilizar o stream. Caso contrário, pedir permissão.
                let streamToUse = microphoneStream; // Tenta usar o stream global de monitoramento
                if (!isSoloMonitoring && !microphoneStream) { // Se não estiver monitorando E não houver stream global
                    streamToUse = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
                    // Não vamos criar um audioContext aqui ainda, a menos que o monitoramento seja ativado
                } else if (isSoloMonitoring && microphoneStream) {
                    // Se está monitorando, microphoneStream já está configurado.
                     console.log("Reutilizando stream do monitoramento para gravação solo.");
                } else if (!isSoloMonitoring && microphoneStream) {
                    // Havia um stream global mas o monitoramento solo não estava ativo, podemos usá-lo
                    console.log("Utilizando stream global pré-existente para gravação solo.");
                }


                if (!streamToUse) { // Fallback caso algo dê errado
                     streamToUse = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
                }

                // Se o monitoramento solo não estiver ativo, mas o global estiver, precisamos parar o global
                // para não ter duas fontes de áudio ou contextos conflitantes.
                // OU, melhor: a gravação solo usa seu próprio contexto e stream se necessário.

                soloAudioChunks = [];
                soloMediaRecorder = new MediaRecorder(streamToUse);

                soloMediaRecorder.ondataavailable = event => {
                    soloAudioChunks.push(event.data);
                };

                soloMediaRecorder.onstart = () => {
                    console.log("Gravação solo iniciada.");
                    soloRecordBtn.innerHTML = '<i class="fas fa-microphone-alt me-2"></i>Gravando...';
                    soloRecordBtn.classList.remove('btn-primary');
                    soloRecordBtn.classList.add('btn-warning');
                    soloRecordBtn.disabled = true;
                    soloStopBtn.style.display = 'inline-block';
                    soloAudioPlayer.currentTime = 0;
                    soloAudioPlayer.play();

                    // <<< INICIAR VISUALIZADOR PARA MODO SOLO >>>
                    startVisualizerWithRecording(true);
                };

                soloMediaRecorder.onstop = async () => {
                    console.log("Gravação solo parada.");
                    soloRecordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar a Cantar';
                    soloRecordBtn.classList.remove('btn-warning');
                    soloRecordBtn.classList.add('btn-primary');
                    soloRecordBtn.disabled = false;
                    soloStopBtn.style.display = 'none';
                    soloAudioPlayer.pause();

                    // <<< PARAR VISUALIZADOR PARA MODO SOLO >>>
                    stopVisualizerWithRecording(true);

                    if (isSoloMonitoring) { // Para o monitoramento se estava ativo
                        toggleSoloMonitor();
                    }
                    // Não parar o stream global (microphoneStream) aqui, pois pode estar sendo usado por outras coisas
                    // ou será parado ao sair do modo solo completamente.
                    // Se streamToUse foi obtido localmente (não era o microphoneStream), podemos pará-lo.
                    if (streamToUse !== microphoneStream) {
                        streamToUse.getTracks().forEach(track => track.stop());
                    }


                    if (soloAudioChunks.length > 0) {
                        const audioBlob = new Blob(soloAudioChunks, { type: 'audio/webm' }); // ou 'audio/ogg; codecs=opus' ou o que for suportado
                        soloAudioChunks = []; // Limpa para a próxima gravação
                        await uploadSoloAudio(audioBlob);
                    } else {
                        console.warn("Nenhum chunk de áudio gravado para o modo solo.");
                        soloAudioLoadingIndicator.style.display = 'none';
                    }
                };

                soloMediaRecorder.onerror = (event) => {
                    console.error("Erro no MediaRecorder solo:", event.error);
                    alert(`Erro na gravação solo: ${event.error.name} - ${event.error.message}`);
                    resetSoloRecordingUI();
                };

                soloMediaRecorder.start();

            } catch (err) {
                console.error("Erro ao iniciar gravação solo:", err);
                alert(`Não foi possível iniciar a gravação. Verifique as permissões do microfone. Erro: ${err.message}`);
                resetSoloRecordingUI();
            }
        }

        function resetSoloRecordingUI() {
            // <<< PARAR VISUALIZADOR SOLO >>>
            stopVisualizerWithRecording(true);

            soloRecordBtn.innerHTML = '<i class="fas fa-play me-2"></i>Começar a Cantar';
            soloRecordBtn.classList.remove('btn-warning');
            soloRecordBtn.classList.add('btn-primary');
            soloRecordBtn.disabled = !(currentSoloSongId && songsData[currentSoloSongId] && songsData[currentSoloSongId].audio_url);
            soloStopBtn.style.display = 'none';
            soloAudioLoadingIndicator.style.display = 'none';
            if (isSoloMonitoring) {
                toggleSoloMonitor(); // Desativa se estiver ativo
            }
        }


        function stopSoloRecordingAndProcess() {
            // <<< PARAR VISUALIZADOR SOLO >>>
            stopVisualizerWithRecording(true);

            if (soloMediaRecorder && soloMediaRecorder.state !== "inactive") {
                soloMediaRecorder.stop();
            }
        }

        async function uploadSoloAudio(audioBlob) {
            if (!currentSoloSongId || !songsData[currentSoloSongId]) {
                alert("Música não identificada para enviar o áudio.");
                return;
            }

            soloAudioLoadingIndicator.style.display = 'block';
            soloUploadStatusMessage.textContent = 'Enviando áudio...';
            soloUploadProgressPercentage.textContent = '0%';

            const formData = new FormData();
            formData.append('audio_file', audioBlob, `solo_recording_${Date.now()}.webm`);
            // Não precisamos de client_id ou lobby_id para o modo solo no upload,
            // mas o backend precisará da song_id para obter a letra original.
            // Poderíamos enviar song_id no FormData ou na URL. Vamos pela URL.

            try {
                // Simulação de progresso de upload (já que fetch não suporta nativamente)
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    if (progress <= 90) { // Não vai a 100% aqui, o "processando" cobre o resto
                        soloUploadProgressPercentage.textContent = `${progress}%`;
                    } else {
                        clearInterval(interval);
                    }
                }, 200);


                // <<< MODIFICAR URL PARA O NOVO ENDPOINT SOLO >>>
                const response = await fetch(`/solo/upload_audio/${currentSoloSongId}`, {
                    method: 'POST',
                    body: formData,
                    // Não precisa de 'Content-Type': 'multipart/form-data', o FormData cuida disso.
                });

                clearInterval(interval); // Limpa o intervalo de progresso simulado
                soloUploadStatusMessage.textContent = 'Processando pontuação...';
                soloUploadProgressPercentage.textContent = `100%`;


                if (!response.ok) {
                    const errorData = await response.json().catch(() => null);
                    throw new Error(`Erro no servidor: ${response.status} ${response.statusText}. Detalhes: ${errorData ? JSON.stringify(errorData.detail) : 'Sem detalhes.'}`);
                }

                const result = await response.json();

                soloAudioLoadingIndicator.style.display = 'none';
                displaySoloResults(result);

            } catch (error) {
                console.error('Erro ao enviar áudio solo:', error);
                alert(`Falha ao enviar áudio para o servidor: ${error.message}`);
                soloAudioLoadingIndicator.style.display = 'none';
                resetSoloRecordingUI();
            }
        }

        function displaySoloResults(result) {
            soloGamePlayArea.style.display = 'none';
            soloResultsArea.style.display = 'block';

            soloScoreDisplay.textContent = `${result.score || 0}%`;

            // Exibir letra original para referência
            const song = songsData[currentSoloSongId];
            if (song) {
                if (song.lrc_url && soloLrcParser) { // Se tínhamos LRC, exibir texto parseado
                    const lrcLines = soloLrcParser.getLyrics().map(line => line.text).join('<br>');
                    soloResultOriginalLyrics.innerHTML = lrcLines;
                } else if (song.letra) { // Senão, a letra simples
                    soloResultOriginalLyrics.innerHTML = song.letra.replace(/\n/g, '<br>');
                } else {
                    soloResultOriginalLyrics.innerHTML = 'Letra original não disponível.';
                }
            } else {
                 soloResultOriginalLyrics.innerHTML = 'Letra original não disponível.';
            }


            soloPlayAgainBtn.onclick = () => {
                // Reiniciar com a mesma música
                soloResultsArea.style.display = 'none';
                startSoloSingingSession(currentSoloSongId);
            };

            soloNewSongBtn.onclick = () => {
                // Voltar para a seleção de músicas solo
                soloResultsArea.style.display = 'none';
                soloSongSelectionArea.style.display = 'block';
                populateSoloSongList(); // Repopular e mostrar lista de músicas
            };
        }

        function resetSoloGameUI() {
            // Esconde todas as sub-seções do jogo solo
            soloSongSelectionArea.style.display = 'block'; // Volta para seleção
            soloGamePlayArea.style.display = 'none';
            soloResultsArea.style.display = 'none';
            soloAudioLoadingIndicator.style.display = 'none';

            // Limpa informações da música atual
            soloCurrentSongTitle.textContent = 'Música';
            soloCurrentSongArtist.textContent = '';
            if(soloAudioPlayer) soloAudioPlayer.src = '';
            soloLyricsDisplay.innerHTML = '';

            // Reseta botões de gravação
            resetSoloRecordingUI();

            // Limpa resultados
            soloScoreDisplay.textContent = '0%';
            soloResultOriginalLyrics.innerHTML = '';

            currentSoloSongId = null;
            if (soloLrcParser) soloLrcParser = null;

            // Garante que o microfone/monitoramento solo seja parado
            stopSoloMicrophone();
        }

        // <<< Funções de Monitoramento de Áudio para Modo Solo >>>
        soloMonitorBtn.addEventListener('click', toggleSoloMonitor);

        async function toggleSoloMonitor() {
            if (!isSoloMonitoring) {
                try {
                    // Se já existe um stream global (ex: do lobby), não pedir de novo, mas criar contexto solo.
                    // No entanto, para evitar conflitos, o modo solo deve gerenciar seu próprio stream e contexto.

                    // Parar qualquer monitoramento global se estiver ativo para evitar conflitos
                    if (isMonitoring) {
                        // Idealmente, chamar uma função que para o monitoramento global
                        // await toggleMonitor(); // Supondo que exista uma função assim para o global
                         console.warn("Monitoramento global estava ativo. O monitoramento solo pode ter comportamento inesperado se ambos usarem o mesmo stream/contexto. Idealmente, pare o global primeiro.");
                    }

                    // O modo solo SEMPRE cria seu próprio stream e contexto para isolamento
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
                    microphoneStream = stream; // Atribui ao global, mas o contexto solo é o que importa aqui
                                            // Alternativamente, `let soloMicrophoneStream = stream;`

                    soloAudioContext = new (window.AudioContext || window.webkitAudioContext)();
                    soloMicrophoneSourceNode = soloAudioContext.createMediaStreamSource(microphoneStream); // Usar o stream recém-criado
                    soloMonitorGainNode = soloAudioContext.createGain();
                    soloMonitorGainNode.gain.value = 1.0; // Volume total, ajuste conforme necessário

                    soloMicrophoneSourceNode.connect(soloMonitorGainNode);
                    soloMonitorGainNode.connect(soloAudioContext.destination);

                    soloMonitorBtn.innerHTML = '<i class="fas fa-volume-up"></i> Parar Retorno';
                    soloMonitorBtn.classList.replace('btn-outline-secondary', 'btn-warning');
                    isSoloMonitoring = true;
                    console.log("Monitoramento solo iniciado.");

                } catch (err) {
                    console.error("Erro ao iniciar monitoramento solo:", err);
                    alert("Não foi possível iniciar o retorno do microfone: " + err.message);
                }
            } else {
                stopSoloMicrophone(); // Chama a função de parada
            }
        }

        function stopSoloMicrophone() {
            if (soloMicrophoneSourceNode) {
                soloMicrophoneSourceNode.disconnect();
                soloMicrophoneSourceNode = null;
            }
            if (soloMonitorGainNode) {
                soloMonitorGainNode.disconnect();
                soloMonitorGainNode = null;
            }
            if (soloAudioContext && soloAudioContext.state !== 'closed') {
                soloAudioContext.close().then(() => console.log("Contexto de áudio solo fechado."));
                soloAudioContext = null;
            }
            // Importante: Parar as trilhas do stream do microfone se ele foi criado especificamente para o modo solo
            // e não está sendo usado pela gravação. Se a gravação estiver ativa, ela gerencia o stream.
            // Se `microphoneStream` é global e pode ser usado por outros, não pare aqui a menos que seja o fim do modo solo.
            // No `exitSoloModeBtn` ou `resetSoloGameUI`, o stream global (microphoneStream) deve ser parado
            // se foi iniciado pelo modo solo.
            if (microphoneStream && isSoloMonitoring) { // Só para se o monitoramento solo estava ativo
                // Se a gravação não estiver usando este stream, podemos pará-lo.
                // Esta lógica pode ser complexa. Uma abordagem mais simples:
                // O stream obtido para monitoramento solo é sempre parado aqui.
                // A gravação obterá seu próprio stream ou reutilizará se a lógica permitir.
                microphoneStream.getTracks().forEach(track => track.stop());
                microphoneStream = null; // Limpa o stream global se o modo solo o controlava
                 console.log("Stream do microfone (originado/usado pelo monitoramento solo) parado.");
            }


            soloMonitorBtn.innerHTML = '<i class="fas fa-ear-listen"></i> Ouvir Retorno';
            soloMonitorBtn.classList.replace('btn-warning', 'btn-outline-secondary');
            isSoloMonitoring = false;
            console.log("Monitoramento solo parado.");
        }


        // <<< EVENT LISTENERS PARA O VISUALIZADOR >>>
        function setupVisualizerEventListeners() {
            // Botão para alternar visualizador multiplayer
            if (toggleVisualizerBtn) {
                toggleVisualizerBtn.addEventListener('click', () => {
                    if (audioVisualizerContainer.style.display === 'none') {
                        audioVisualizerContainer.style.display = 'block';
                        toggleVisualizerBtn.innerHTML = '<i class="fas fa-eye-slash me-1"></i>Ocultar Visualizador';

                        // Iniciar visualização se houver stream ativo
                        if (microphoneStream && audioContext) {
                            setupAudioAnalyser(audioContext, microphoneStream, false);
                            isVisualizerActive = true;
                            startAudioVisualization(false);
                        }
                    } else {
                        audioVisualizerContainer.style.display = 'none';
                        toggleVisualizerBtn.innerHTML = '<i class="fas fa-eye me-1"></i>Mostrar Visualizador';
                        stopAudioVisualization(false);
                    }
                });
            }

            // Botão para alternar visualizador solo
            if (soloToggleVisualizerBtn) {
                soloToggleVisualizerBtn.addEventListener('click', () => {
                    if (soloAudioVisualizerContainer.style.display === 'none') {
                        soloAudioVisualizerContainer.style.display = 'block';
                        soloToggleVisualizerBtn.innerHTML = '<i class="fas fa-eye-slash me-1"></i>Ocultar Visualizador';

                        // Iniciar visualização se houver stream ativo
                        if (microphoneStream && soloAudioContext) {
                            setupAudioAnalyser(soloAudioContext, microphoneStream, true);
                            isSoloVisualizerActive = true;
                            startAudioVisualization(true);
                        }
                    } else {
                        soloAudioVisualizerContainer.style.display = 'none';
                        soloToggleVisualizerBtn.innerHTML = '<i class="fas fa-eye me-1"></i>Mostrar Visualizador';
                        stopAudioVisualization(true);
                    }
                });
            }


        }

        // <<< INTEGRAÇÃO DO VISUALIZADOR COM GRAVAÇÃO >>>
        async function startVisualizerWithRecording(isSolo = false) {
            console.log('[Visualizer] startVisualizerWithRecording chamado:', {
                isSolo,
                hasMicrophoneStream: !!microphoneStream,
                microphoneStreamActive: microphoneStream?.active,
                hasAudioContext: !!audioContext,
                hasSoloAudioContext: !!soloAudioContext,
                audioContextState: audioContext?.state,
                soloAudioContextState: soloAudioContext?.state
            });

            const container = isSolo ? soloAudioVisualizerContainer : audioVisualizerContainer;
            let context = isSolo ? soloAudioContext : audioContext;

            // CRIAR CONTEXTO SE NÃO EXISTIR (usando mesma configuração do teste)
            if (!context) {
                console.log('[Visualizer] Criando contexto de áudio:', { isSolo });
                try {
                    context = new (window.AudioContext || window.webkitAudioContext)();
                    if (isSolo) {
                        soloAudioContext = context;
                    } else {
                        audioContext = context;
                    }
                    console.log('[Visualizer] Contexto criado:', { state: context.state, sampleRate: context.sampleRate });
                } catch (error) {
                    console.error('[Visualizer] Erro ao criar contexto:', error);
                    return;
                }
            }

            // RESUMIR CONTEXTO SE SUSPENSO
            if (context.state === 'suspended') {
                console.log('[Visualizer] Resumindo contexto suspenso...');
                try {
                    await context.resume();
                    console.log('[Visualizer] Contexto resumido, estado atual:', context.state);
                } catch (error) {
                    console.error('[Visualizer] Erro ao resumir contexto:', error);
                }
            }

            // SE NÃO HOUVER STREAM, CRIAR UM NOVO (usando configuração do teste)
            if (!microphoneStream || !microphoneStream.active) {
                console.log('[Visualizer] Criando novo stream de áudio...');
                try {
                    // Usar configuração mais permissiva (mesma do teste que funcionou)
                    microphoneStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    console.log('[Visualizer] Novo stream criado:', {
                        id: microphoneStream.id,
                        active: microphoneStream.active,
                        tracks: microphoneStream.getTracks().length
                    });
                } catch (error) {
                    console.error('[Visualizer] Erro ao criar stream:', error);
                    return;
                }
            }

            console.log('[Visualizer] Elementos para visualização:', {
                hasContainer: !!container,
                containerDisplay: container?.style.display,
                hasContext: !!context,
                contextState: context?.state,
                hasStream: !!microphoneStream,
                streamActive: microphoneStream?.active
            });

            // Mostrar visualizador automaticamente quando começar a gravar
            if (container) {
                console.log('[Visualizer] Mostrando container do visualizador');
                container.style.display = 'block';
                const toggleBtn = isSolo ? soloToggleVisualizerBtn : toggleVisualizerBtn;
                if (toggleBtn) {
                    toggleBtn.innerHTML = '<i class="fas fa-eye-slash me-1"></i>Ocultar Visualizador';
                }
            }

            // Configurar e iniciar visualização
            if (microphoneStream && context) {
                console.log('[Visualizer] Configurando analisador e iniciando visualização');
                setupAudioAnalyser(context, microphoneStream, isSolo);
                if (isSolo) {
                    isSoloVisualizerActive = true;
                } else {
                    isVisualizerActive = true;
                }
                startAudioVisualization(isSolo);
            } else {
                console.warn('[Visualizer] Não foi possível iniciar visualização:', {
                    hasMicrophoneStream: !!microphoneStream,
                    hasContext: !!context,
                    microphoneStreamActive: microphoneStream?.active
                });
            }
        }

        function stopVisualizerWithRecording(isSolo = false) {
            stopAudioVisualization(isSolo);
        }



        // Função para carregar e exibir músicas (usada por ambos os modos)
        async function loadAndDisplaySongs() {
            // ... existing code ...
            // Sincronizar LRC com áudio (para modo lobby/duelo)
            // TODO: Garantir que isso só rode se estivermos na game-section e não na solo-game-section
            audioPlayer.addEventListener('timeupdate', () => {
                if (currentLrcParser && audioPlayer.currentTime > 0 && sections.game.classList.contains('active')) {
                    const currentIndex = currentLrcParser.select(audioPlayer.currentTime);
                    highlightLyricLine(currentIndex); // Função original para o lobby
                }
            });

            // Monitoramento de áudio (para modo lobby/duelo)
            monitorBtn.addEventListener('click', toggleMonitor); // Usa a função de monitoramento original/global

            async function toggleMonitor() { // Função de monitoramento ORIGINAL/GLOBAL
                // ... existing code ...
            }
        }

        // <<< INICIALIZAÇÃO >>>
        document.addEventListener('DOMContentLoaded', () => {
            initializeAudioVisualizer();
            setupVisualizerEventListeners();
        });

        // ... existing code ...
    </script>

    <style>
    /* Removendo estilos anteriores e adicionando os novos */
    body {
        font-family: 'Inter', sans-serif;
        background-color: #1a1a1a; /* Cor de fundo geral como no exemplo */
    }

    /* Estilos gerais do seu app que devem ser mantidos (ex: .container-main, .header-icon, etc.) */
    /* Seus estilos existentes de .container-main, .main-content-wrapper, etc. aqui */
    /* ... por exemplo ... */
    .container-main {
        max-width: 900px; /* Ou o que você tinha */
        margin: 0 auto;
        padding: 20px;
    }
    header .header-icon {
        font-size: 2.5rem;
        margin-right: 15px;
        color: var(--primary-accent-color, #7289da); /* Exemplo de cor */
    }
    header h1 {
        color: var(--primary-text-color, #ffffff);
    }
    .main-title, .main-subtitle {
        color: var(--secondary-text-color, #b9bbbe);
        text-align: center;
    }
    .main-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .main-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    /* Adaptação do card shadow-sm para o novo estilo, ou remover se não for mais usado assim */
    .card.shadow-sm {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2); /* Sombra do apple-style-container */
        border: none; /* A imagem de referência não mostra bordas nos cards externos */
        background-color: #2c2c2e; /* Cor de fundo do apple-style-container */
        border-radius: 12px;
        /* Cuidado para não sobrescrever o padding interno específico dos cards */
    }

    /* Título das seções (Modo Solo, Selecione uma Música) */
    /* O HTML gerado pelo JS vai usar apple-style-title e apple-style-list-header */

    /* Botão Sair do Modo Solo */
    /* O HTML gerado usará apple-style-button */
    
    /* Estilos do Apple Style que você forneceu */
    .apple-style-container {
        background-color: #2c2c2e;
        border-radius: 12px;
        padding: 20px;
        max-width: 400px; /* Isso será aplicado aos contêineres de música */
        margin: 20px auto; /* Margem ajustada para seções dentro da página */
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }
    .apple-style-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
    }
    .apple-style-title {
        font-size: 1.5rem; 
        font-weight: 700;
        color: #f2f2f7;
    }
    .apple-style-button {
        background-color: #3a3a3c; 
        color: #f2f2f7;
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-size: 0.875rem; 
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
    }
    .apple-style-button:hover {
        background-color: #4a4a4c;
    }
    .apple-style-button .material-icons-outlined {
        font-size: 1.125rem;
    }
    .apple-style-list-header {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #aeaeb2; 
        font-size: 1.125rem; 
        font-weight: 600;
        margin-bottom: 16px;
    }
    .apple-style-list-header .material-icons-outlined {
        font-size: 1.5rem; 
        color: #0a84ff;
    }
    .apple-style-list-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #3a3a3c; 
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
    }
    .apple-style-list-item:last-child {
        border-bottom: none;
    }
    .apple-style-list-item:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
    .apple-style-song-info {
        display: flex;
        flex-direction: column;
    }
    .apple-style-song-title {
        font-size: 1rem; 
        font-weight: 500;
        color: #f2f2f7;
    }
    .apple-style-song-artist {
        font-size: 0.875rem; 
        color: #aeaeb2;
    }
    .apple-style-play-icon {
        color: #0a84ff; 
        font-size: 1.75rem;
    }

    /* Estilos para a seção de jogo solo (card externo) */
    /* #solo-game-section .card { ... } */ /* Removido ou adaptado */
    /* #solo-game-section .card-header { ... } */ /* Removido ou adaptado */

    /* Removendo estilos específicos do .song-list-container que foram substituídos */
    /* .song-list-container { ... } */
    /* .song-list-container .list-group-item { ... } */
    /* etc. */

    /* Manter a scrollbar se o contêiner interno da lista precisar */
    /* Se a lista de músicas (ul) estiver dentro de um div com altura fixa e overflow */
    .song-list-scrollable-area {
        max-height: 60vh; /* Ou a altura desejada */
        overflow-y: auto;
        padding-right: 5px; /* Espaço para a scrollbar não sobrepor o conteúdo */
    }
    .song-list-scrollable-area::-webkit-scrollbar {
        width: 6px;
    }
    .song-list-scrollable-area::-webkit-scrollbar-track {
        background: transparent;
    }
    .song-list-scrollable-area::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
    }
    .song-list-scrollable-area::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.3);
    }
    
    /* Esconder seções não ativas */
    .section {
        display: none; 
        /* padding: 20px; */ /* Removido, apple-style-container tem seu padding */
        /* background-color: var(--discord-dark); */ /* Removido */
        /* box-shadow: 0 4px 12px rgba(0,0,0,0.2); */ /* Removido */
        /* margin-bottom: 25px; */ /* Removido */
        animation: fadeIn 0.5s ease-out;
    }
    .section.active {
        display: block;
    }

    /* Outros estilos que você precisa manter */
    #soloLyricsDisplay {
        background-color: var(--discord-dark-3, #202225);
        border: 1px solid var(--discord-gray, #4f545c);
        padding: 15px;
        border-radius: 5px;
        color: var(--discord-light-gray, #b9bbbe);
        min-height: 100px; 
    }
    #soloLyricsDisplay p.active-lyric {
        color: var(--discord-yellow, #faa61a); 
        font-weight: bold;
        background-color: rgba(250, 230, 100, 0.1); 
        padding: 2px 5px;
        border-radius: 3px;
    }
    #solo-results-area .score {
        color: var(--discord-green, #43b581); 
    }
    .lobby-options-container {
        display: flex;
        flex-wrap: wrap; 
        gap: 20px; 
        justify-content: center; 
    }
    .lobby-option-card {
        flex: 1; 
        min-width: 280px; 
        max-width: 350px; 
        background-color: #2c2c2e; /* Consistente com apple-style */
        border-radius: 12px;
        padding: 20px;
        color: #f2f2f7;
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }
    .lobby-option-card h3 {
        color: #f2f2f7;
    }
    .lobby-option-card p {
        color: #aeaeb2;
    }
    .lobby-card-icon-wrapper {
        /* Seus estilos para icon wrapper */
        margin-bottom: 10px;
    }
    .action-button {
        /* Seus estilos para action-button, adaptados */
        background-color: #0a84ff; /* Azul Apple */
        color: white;
        border-radius: 8px;
        padding: 10px 20px;
    }
    .action-button:hover {
        background-color: #0073e6; /* Azul Apple mais escuro */
    }

    /* Adicione aqui quaisquer outros estilos globais do seu tema escuro que foram removidos */
    /* por engano ou que precisam ser ajustados para coexistir com o estilo Apple. */
    
    /* Estilos para o Visualizador de Áudio - adaptando para o novo tema */
    .audio-visualizer-wrapper {
        background-color: #3a3a3c; /* Cor mais clara do tema Apple para contraste */
        border: 1px solid #4a4a4c;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        margin-top: 20px; /* Espaçamento */
    }

    #audioVisualizer, #soloAudioVisualizer {
        width: 100%;
        max-width: 600px;
        height: 150px;
        background-color: #2c2c2e; /* Fundo do canvas */
        border: 1px solid #3a3a3c;
        border-radius: 5px;
        display: block;
        margin: 0 auto 10px auto; /* Margem inferior */
    }

    /* Ajustar card do lobby de jogo */
    #game-section > .card {
        background-color: #2c2c2e;
        border-radius: 12px;
        padding: 0; /* Resetar padding para o card-body controlar */
        color: #f2f2f7;
    }
    #game-section > .card > .card-header {
        background-color: transparent; /* Ou uma cor de header sutil do tema Apple */
        border-bottom: 1px solid #3a3a3c;
        color: #f2f2f7;
        font-weight: 600;
        padding: 16px 20px;
    }
    #game-section > .card > .card-body {
        padding: 20px;
    }
    .player-card {
        background-color: #3a3a3c;
        padding: 15px;
        border-radius: 8px;
        color: #f2f2f7;
        margin-bottom: 10px;
    }
    .player-card h5 {
        color: #f2f2f7;
    }
    .player-card small {
        color: #aeaeb2;
    }
    #lobbyStatusMessage {
        background-color: #3a3a3c !important; /* Forçar sobreposição se necessário */
        border-color: #4a4a4c !important;
        color: #f2f2f7 !important;
        border-radius: 8px;
    }

    </style>
</body>
</html>